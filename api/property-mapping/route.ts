import { NextRequest, NextResponse } from 'next/server';

/**
 * CMS Property Mapping API
 * 
 * Provides mapping pins data for displaying properties on a map.
 * Supports searching by area or fetching specific property IDs.
 * 
 * @route POST /api/property-mapping
 */
export async function POST(request: NextRequest) {
  try {
    // Check for API key
    const API_KEY = process.env.REAL_ESTATE_API_KEY;
    if (!API_KEY) {
      console.error('Error: REAL_ESTATE_API_KEY is not set in environment variables.');
      return NextResponse.json(
        { 
          success: false,
          statusCode: 500,
          message: 'API key is not configured.' 
        },
        { status: 500 }
      );
    }
    
    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Invalid JSON in request body'
        },
        { status: 400 }
      );
    }
    
    // Validate that either IDs or search parameters are provided
    if (!body.ids && !body.search) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Either property IDs or search parameters are required'
        },
        { status: 400 }
      );
    }
    
    console.log(`Making request to Property Mapping API`);
    
    // Set up request timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout
    
    // Make request to external API
    const response = await fetch('https://api.realestateapi.com/v2/PropertyMapping', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-KEY': API_KEY
      },
      body: JSON.stringify(body),
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    // Handle different response statuses
    if (!response.ok) {
      // Special handling for 403 errors (likely due to plan limitations)
      if (response.status === 403) {
        return NextResponse.json(
          {
            success: false,
            statusCode: 403,
            message: 'This API requires the Growth+ plan or specific permissions. Contact your API provider for access.'
          },
          { status: 403 }
        );
      }
      
      let errorMessage = `API error: ${response.status} ${response.statusText}`;
      
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch {
        // If we can't parse the error response, use the default message
      }
      
      console.error(errorMessage);
      
      return NextResponse.json(
        {
          success: false,
          statusCode: response.status,
          message: errorMessage
        },
        { status: response.status }
      );
    }
    
    // Parse and return the data
    const data = await response.json();
    
    return NextResponse.json({
      success: true,
      data
    });
    
  } catch (error: any) {
    // Handle request timeout
    if (error.name === 'AbortError') {
      return NextResponse.json(
        {
          success: false,
          statusCode: 504,
          message: 'Property mapping API request timed out'
        },
        { status: 504 }
      );
    }
    
    // Handle other errors
    console.error('Property mapping API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        statusCode: 500,
        message: error.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}