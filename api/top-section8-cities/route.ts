import { NextRequest, NextResponse } from 'next/server';

// Interface for HUD FMR data
interface FMRData {
  state: string;
  city: string;
  metro_name?: string;
  fmr_0br?: number;
  fmr_1br?: number;
  fmr_2br?: number;
  fmr_3br?: number;
  fmr_4br?: number;
  year: number;
}

// Interface for city analysis
interface CityAnalysis {
  city: string;
  state: string;
  avgFMR: number;
  score: number;
  rank: number;
  fmrData: {
    fmr_1br: number;
    fmr_2br: number;
    fmr_3br: number;
    fmr_4br: number;
  };
}

/**
 * Calculate Section 8 opportunity score for a city based on FMR data
 */
function calculateSection8Score(fmrData: FMRData, nationalAvg: number): number {
  // Weight different bedroom counts (2BR and 3BR are most common for Section 8)
  const weights = {
    fmr_1br: 0.15,
    fmr_2br: 0.35,
    fmr_3br: 0.35,
    fmr_4br: 0.15
  };

  const weightedFMR = 
    (fmrData.fmr_1br || 0) * weights.fmr_1br +
    (fmrData.fmr_2br || 0) * weights.fmr_2br +
    (fmrData.fmr_3br || 0) * weights.fmr_3br +
    (fmrData.fmr_4br || 0) * weights.fmr_4br;

  // Score factors:
  // 1. FMR amount (higher = better for landlords)
  // 2. Consistency across bedroom types
  // 3. Above national average indicates strong voucher support
  
  const fmrScore = Math.min(weightedFMR / 1000, 2.5); // Cap at $2500 weighted avg
  const consistencyScore = calculateConsistencyScore(fmrData);
  const nationalComparisonScore = Math.min(weightedFMR / nationalAvg, 2.0);
  
  // Combined score (0-100)
  return Math.round(
    (fmrScore * 30 + consistencyScore * 20 + nationalComparisonScore * 50)
  );
}

/**
 * Calculate consistency score based on FMR distribution
 */
function calculateConsistencyScore(fmrData: FMRData): number {
  const values = [
    fmrData.fmr_1br || 0,
    fmrData.fmr_2br || 0,
    fmrData.fmr_3br || 0,
    fmrData.fmr_4br || 0
  ].filter(v => v > 0);

  if (values.length < 2) return 0;

  const avg = values.reduce((a, b) => a + b, 0) / values.length;
  const variance = values.reduce((acc, val) => acc + Math.pow(val - avg, 2), 0) / values.length;
  const stdDev = Math.sqrt(variance);
  
  // Lower standard deviation = higher consistency = better score
  return Math.max(0, 100 - (stdDev / avg) * 100);
}

/**
 * Fetch FMR data from HUD API for multiple states
 */
async function fetchFMRData(): Promise<FMRData[]> {
  const targetStates = [
    'AL', 'AR', 'AZ', 'CO', 'FL', 'GA', 'IL', 'IN', 'KS', 'KY', 
    'LA', 'MI', 'MO', 'MS', 'NC', 'OH', 'OK', 'PA', 'SC', 'TN', 
    'TX', 'VA', 'WV'
  ]; // States known for Section 8 opportunities

  const allFMRData: FMRData[] = [];
  const currentYear = new Date().getFullYear();

  for (const state of targetStates) {
    try {
      const response = await fetch(
        `https://www.huduser.gov/hudapi/public/fmr/data/${state}?year=${currentYear}`,
        {
          headers: {
            'User-Agent': 'Section8Investor/1.0'
          }
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.data && Array.isArray(data.data)) {
          allFMRData.push(...data.data);
        }
      }
    } catch (error) {
      console.error(`Error fetching FMR data for ${state}:`, error);
    }
  }

  return allFMRData;
}

/**
 * Analyze cities and return top 15 Section 8 investment opportunities
 */
function analyzeTopSection8Cities(fmrData: FMRData[]): CityAnalysis[] {
  // Group by city/state and aggregate data
  const cityMap = new Map<string, FMRData[]>();
  
  fmrData.forEach(item => {
    const key = `${item.city},${item.state}`;
    if (!cityMap.has(key)) {
      cityMap.set(key, []);
    }
    cityMap.get(key)!.push(item);
  });

  // Calculate national average for comparison
  const allWeightedFMRs = fmrData.map(item => {
    return (
      (item.fmr_1br || 0) * 0.15 +
      (item.fmr_2br || 0) * 0.35 +
      (item.fmr_3br || 0) * 0.35 +
      (item.fmr_4br || 0) * 0.15
    );
  }).filter(v => v > 0);
  
  const nationalAvg = allWeightedFMRs.reduce((a, b) => a + b, 0) / allWeightedFMRs.length;

  // Analyze each city
  const cityAnalyses: CityAnalysis[] = [];
  
  cityMap.forEach((cityFMRData, cityStateKey) => {
    const [city, state] = cityStateKey.split(',');
    
    // Use the most recent/complete data for the city
    const bestRecord = cityFMRData.reduce((best, current) => {
      const currentCompleteness = [current.fmr_1br, current.fmr_2br, current.fmr_3br, current.fmr_4br]
        .filter(v => v && v > 0).length;
      const bestCompleteness = [best.fmr_1br, best.fmr_2br, best.fmr_3br, best.fmr_4br]
        .filter(v => v && v > 0).length;
      
      return currentCompleteness > bestCompleteness ? current : best;
    });

    // Only include cities with at least 2BR and 3BR data (most important for Section 8)
    if (!bestRecord.fmr_2br || !bestRecord.fmr_3br) return;

    const score = calculateSection8Score(bestRecord, nationalAvg);
    const avgFMR = (
      (bestRecord.fmr_1br || 0) * 0.15 +
      (bestRecord.fmr_2br || 0) * 0.35 +
      (bestRecord.fmr_3br || 0) * 0.35 +
      (bestRecord.fmr_4br || 0) * 0.15
    );

    cityAnalyses.push({
      city,
      state,
      avgFMR,
      score,
      rank: 0, // Will be set after sorting
      fmrData: {
        fmr_1br: bestRecord.fmr_1br || 0,
        fmr_2br: bestRecord.fmr_2br || 0,
        fmr_3br: bestRecord.fmr_3br || 0,
        fmr_4br: bestRecord.fmr_4br || 0,
      }
    });
  });

  // Sort by score and assign ranks
  cityAnalyses.sort((a, b) => b.score - a.score);
  cityAnalyses.forEach((city, index) => {
    city.rank = index + 1;
  });

  // Return top 15
  return cityAnalyses.slice(0, 15);
}

/**
 * Update database with new top 15 cities
 */
async function updateAppPreferences(topCities: CityAnalysis[]) {
  try {
    // Format cities array as required: ["1", "city", "state", "2", "city", "state", ...]
    const formattedArray: string[] = [];
    topCities.forEach((city, index) => {
      formattedArray.push(
        (index + 1).toString(),
        city.city,
        city.state
      );
    });

    // This would integrate with your database
    // For now, we'll return the data structure
    return {
      id: "default",
      top15CitiesStates: JSON.stringify(formattedArray),
      topCitiesCheckTimestamp: Date.now(),
      updatedAt: Date.now()
    };
  } catch (error) {
    console.error('Error updating app preferences:', error);
    throw error;
  }
}

/**
 * Main API handler
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🏙️  Starting Top Section 8 Cities analysis...');
    
    // Fetch FMR data from HUD
    const fmrData = await fetchFMRData();
    console.log(`📊 Retrieved FMR data for ${fmrData.length} areas`);
    
    if (fmrData.length === 0) {
      return NextResponse.json({
        error: 'No FMR data available'
      }, { status: 500 });
    }

    // Analyze and rank cities
    const topCities = analyzeTopSection8Cities(fmrData);
    console.log(`🏆 Analyzed and ranked ${topCities.length} cities`);

    // Update database
    const updatedPreferences = await updateAppPreferences(topCities);
    console.log('💾 Updated app preferences in database');

    // Return results
    return NextResponse.json({
      success: true,
      message: `Successfully updated top 15 Section 8 cities`,
      topCities: topCities.map(city => ({
        rank: city.rank,
        city: city.city,
        state: city.state,
        score: city.score,
        avgFMR: Math.round(city.avgFMR),
        fmrBreakdown: city.fmrData
      })),
      databaseUpdate: updatedPreferences,
      lastUpdated: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error in top-section8-cities API:', error);
    return NextResponse.json({
      error: 'Failed to generate top Section 8 cities',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
