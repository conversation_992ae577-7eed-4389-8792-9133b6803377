import { NextRequest, NextResponse } from 'next/server';

const CSV_GENERATOR_API_URL = 'https://api.realestateapi.com/v2/CSVBuilder';

interface CSVGeneratorRequest {
  // File configuration
  file_name: string; // 5-60 characters
  
  // Field mapping - array of column names to include
  map: string[];
  
  // Webhook for completion notification
  webcomplete_url?: string;
  
  // Search parameters (same as PropertySearch API)
  searchParams: {
    // Location parameters
    city?: string;
    state?: string;
    zip?: string;
    county?: string;
    latitude?: number;
    longitude?: number;
    radius?: number;
    
    // Property filters
    property_type?: string;
    beds_min?: number;
    beds_max?: number;
    baths_min?: number;
    baths_max?: number;
    value_min?: number;
    value_max?: number;
    
    // Additional filters
    mls_active?: boolean;
    absentee_owner?: boolean;
    corporate_owned?: boolean;
    vacant?: boolean;
    foreclosure?: boolean;
    [key: string]: any; // Allow any additional filters
  };
}

/**
 * CSV Generator API
 * 
 * Generates CSV files with bulk property data based on search criteria.
 * Supports large datasets with async processing and webhook notifications.
 * 
 * @route POST /api/csv-generator
 */
export async function POST(request: NextRequest) {
  try {
    // Check for API key
    const API_KEY = process.env.REAL_ESTATE_API_KEY;
    if (!API_KEY) {
      console.error('Error: REAL_ESTATE_API_KEY is not set in environment variables.');
      return NextResponse.json(
        { 
          success: false,
          statusCode: 500,
          message: 'API key is not configured.' 
        },
        { status: 500 }
      );
    }
    
    // Parse request body
    let body: CSVGeneratorRequest;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Invalid JSON in request body'
        },
        { status: 400 }
      );
    }
    
    // Validate required parameters
    if (!body.file_name || body.file_name.length < 5 || body.file_name.length > 60) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'file_name must be between 5 and 60 characters'
        },
        { status: 400 }
      );
    }
    
    if (!body.map || !Array.isArray(body.map) || body.map.length === 0) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'map array must contain at least one field name'
        },
        { status: 400 }
      );
    }
    
    if (!body.searchParams || Object.keys(body.searchParams).length === 0) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'searchParams must contain at least one search criterion'
        },
        { status: 400 }
      );
    }
    
    // Build CSV generation parameters
    const csvParams = {
      file_name: body.file_name,
      map: body.map,
      webcomplete_url: body.webcomplete_url,
      ...body.searchParams // Spread all search parameters
    };
    
    // Set up headers
    const headers = {
      'Content-Type': 'application/json',
      'X-API-KEY': API_KEY
    };
    
    console.log(`Generating CSV: ${body.file_name} with ${body.map.length} fields`);
    
    // Make request to CSV Generator API
    const response = await fetch(CSV_GENERATOR_API_URL, {
      method: 'POST',
      headers,
      body: JSON.stringify(csvParams)
    });
    
    // Check for successful response
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`CSV Generator API error: ${response.status} - ${errorText}`);
      
      return NextResponse.json(
        {
          success: false,
          statusCode: response.status,
          message: `CSV Generator API error: ${response.statusText}`
        },
        { status: response.status }
      );
    }
    
    // Parse response
    const data = await response.json();
    
    // Return formatted response
    return NextResponse.json({
      success: true,
      data: {
        jobId: data.jobId || data.job_id,
        fileName: body.file_name,
        status: data.status || 'processing',
        downloadUrl: data.downloadUrl || data.download_url,
        webhookUrl: body.webcomplete_url,
        estimatedRows: data.estimatedRows || data.estimated_rows,
        fieldCount: body.map.length,
        message: 'CSV generation job submitted successfully. You will be notified via webhook when complete.'
      }
    });
    
  } catch (error: any) {
    console.error('CSV Generator API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        statusCode: 500,
        message: error.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to check CSV generation job status
 */
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const jobId = searchParams.get('jobId');
  
  if (!jobId) {
    return NextResponse.json(
      {
        success: false,
        statusCode: 400,
        message: 'jobId parameter is required'
      },
      { status: 400 }
    );
  }
  
  // Note: This would need to be implemented based on the actual API provider's status endpoint
  // For now, return a placeholder response
  return NextResponse.json({
    success: true,
    data: {
      jobId,
      status: 'processing',
      message: 'Job status endpoint not yet implemented by API provider'
    }
  });
}
