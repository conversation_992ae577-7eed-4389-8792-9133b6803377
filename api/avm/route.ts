import { NextRequest, NextResponse } from 'next/server';

const AVM_API_URL = 'https://api.realestateapi.com/v2/LenderGradeAVM';
const BULK_AVM_API_URL = 'https://api.realestateapi.com/v2/BulkLenderGradeAVM';

interface AVMRequest {
  // Property identification (one required)
  id?: string;
  address?: string;
  
  // Address parts
  house?: string;
  street?: string;
  city?: string;
  state?: string;
  zip?: string;
  
  // AVM options
  comparableCount?: number; // Number of comparables to use (default: 10)
  radius?: number; // Search radius in miles (default: 1)
  confidenceThreshold?: number; // Minimum confidence level (0-100)
}

interface BulkAVMRequest {
  items: AVMRequest[];
  webhookUrl?: string;
}

/**
 * Lender Grade AVM (Automated Valuation Model) API
 * 
 * Provides professional-grade property valuations using advanced algorithms
 * and comparable sales analysis. Returns confidence scores and value ranges.
 * 
 * @route POST /api/avm
 */
export async function POST(request: NextRequest) {
  try {
    // Check for API key
    const API_KEY = process.env.REAL_ESTATE_API_KEY;
    if (!API_KEY) {
      console.error('Error: REAL_ESTATE_API_KEY is not set in environment variables.');
      return NextResponse.json(
        { 
          success: false,
          statusCode: 500,
          message: 'API key is not configured.' 
        },
        { status: 500 }
      );
    }
    
    // Parse request body
    let body: AVMRequest | BulkAVMRequest;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Invalid JSON in request body'
        },
        { status: 400 }
      );
    }
    
    // Determine if this is a bulk request
    const isBulk = 'items' in body && Array.isArray(body.items);
    
    if (isBulk) {
      // Handle bulk AVM request
      const bulkBody = body as BulkAVMRequest;
      
      if (bulkBody.items.length === 0) {
        return NextResponse.json(
          {
            success: false,
            statusCode: 400,
            message: 'Bulk request must include at least one item'
          },
          { status: 400 }
        );
      }
      
      if (bulkBody.items.length > 1000) {
        return NextResponse.json(
          {
            success: false,
            statusCode: 400,
            message: 'Maximum 1000 items allowed in bulk request'
          },
          { status: 400 }
        );
      }
      
      // Build bulk request parameters
      const bulkParams = {
        items: bulkBody.items.map(item => formatAVMItem(item)),
        webhookUrl: bulkBody.webhookUrl
      };
      
      // Set up headers
      const headers = {
        'Content-Type': 'application/json',
        'X-API-KEY': API_KEY
      };
      
      console.log(`Executing Bulk AVM for ${bulkBody.items.length} properties`);
      
      // Make request to Bulk AVM API
      const response = await fetch(BULK_AVM_API_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(bulkParams)
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Bulk AVM API error: ${response.status} - ${errorText}`);
        
        return NextResponse.json(
          {
            success: false,
            statusCode: response.status,
            message: `Bulk AVM API error: ${response.statusText}`
          },
          { status: response.status }
        );
      }
      
      const data = await response.json();
      
      return NextResponse.json({
        success: true,
        mode: 'bulk',
        data: {
          jobId: data.jobId || data.job_id,
          status: data.status || 'processing',
          itemCount: bulkBody.items.length,
          webhookUrl: bulkBody.webhookUrl || null,
          message: 'Bulk AVM job submitted successfully. Use the job ID to retrieve results.'
        }
      });
      
    } else {
      // Handle single AVM request
      const singleBody = body as AVMRequest;
      
      // Validate request
      if (!singleBody.id && !singleBody.address && !singleBody.house) {
        return NextResponse.json(
          {
            success: false,
            statusCode: 400,
            message: 'Request must include either a property ID, full address, or address parts'
          },
          { status: 400 }
        );
      }
      
      // Build request parameters
      const avmParams = formatAVMItem(singleBody);
      
      // Set up headers
      const headers = {
        'Content-Type': 'application/json',
        'X-API-KEY': API_KEY
      };
      
      console.log('Executing AVM for:', singleBody.id || singleBody.address || 'address parts');
      
      // Set up request timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout
      
      try {
        // Make request to AVM API
        const response = await fetch(AVM_API_URL, {
          method: 'POST',
          headers,
          body: JSON.stringify(avmParams),
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        // Check for successful response
        if (!response.ok) {
          const errorText = await response.text();
          console.error(`AVM API error: ${response.status} - ${errorText}`);
          
          return NextResponse.json(
            {
              success: false,
              statusCode: response.status,
              message: `AVM API error: ${response.statusText}`
            },
            { status: response.status }
          );
        }
        
        // Parse response
        const data = await response.json();
        
        // Return formatted response
        return NextResponse.json({
          success: true,
          data: {
            // Property identification
            propertyId: data.propertyId || data.id,
            address: data.address,
            
            // Valuation results
            valuation: {
              estimatedValue: data.estimatedValue || data.value,
              lowValue: data.lowValue || data.valueLow,
              highValue: data.highValue || data.valueHigh,
              confidence: data.confidence || data.confidenceScore,
              valuationDate: data.valuationDate || new Date().toISOString()
            },
            
            // Value trends
            trends: {
              monthlyChange: data.monthlyChange,
              quarterlyChange: data.quarterlyChange,
              annualChange: data.annualChange,
              fiveYearChange: data.fiveYearChange
            },
            
            // Comparable properties used
            comparables: data.comparables || [],
            comparableCount: data.comparableCount || data.comparables?.length || 0,
            
            // Market context
            marketData: {
              medianValue: data.medianValue,
              averageValue: data.averageValue,
              pricePerSqFt: data.pricePerSqFt,
              rentEstimate: data.rentEstimate,
              grossYield: data.grossYield
            },
            
            // Property details (if included)
            propertyDetails: {
              bedrooms: data.bedrooms,
              bathrooms: data.bathrooms,
              squareFeet: data.squareFeet,
              lotSize: data.lotSize,
              yearBuilt: data.yearBuilt,
              propertyType: data.propertyType
            },
            
            // Model metadata
            modelInfo: {
              modelVersion: data.modelVersion,
              lastUpdated: data.lastUpdated,
              dataQuality: data.dataQuality,
              warnings: data.warnings || []
            },
            
            // Raw response for debugging
            _raw: data
          },
          executionTime: data.executionTimeMS || null
        });
        
      } catch (error: any) {
        clearTimeout(timeoutId);
        
        // Handle timeout
        if (error.name === 'AbortError') {
          return NextResponse.json(
            {
              success: false,
              statusCode: 504,
              message: 'AVM API request timed out'
            },
            { status: 504 }
          );
        }
        
        throw error;
      }
    }
    
  } catch (error: any) {
    console.error('AVM API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        statusCode: 500,
        message: error.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * Format AVM item for API request
 */
function formatAVMItem(item: AVMRequest): Record<string, any> {
  const formatted: Record<string, any> = {};
  
  // Property identification
  if (item.id) {
    formatted.id = item.id;
  } else if (item.address) {
    formatted.address = item.address;
  } else {
    // Address parts
    if (item.house) formatted.house = item.house;
    if (item.street) formatted.street = item.street;
    if (item.city) formatted.city = item.city;
    if (item.state) formatted.state = item.state;
    if (item.zip) formatted.zip = item.zip;
  }
  
  // AVM options
  if (item.comparableCount) formatted.comparableCount = item.comparableCount;
  if (item.radius) formatted.radius = item.radius;
  if (item.confidenceThreshold) formatted.confidenceThreshold = item.confidenceThreshold;
  
  return formatted;
} 