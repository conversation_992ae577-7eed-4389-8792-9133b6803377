import { NextRequest, NextResponse } from 'next/server';

const BULK_SKIPTRACE_API_URL = 'https://api.realestateapi.com/v1/BulkSkipTrace';
const BULK_SKIPTRACE_AWAIT_API_URL = 'https://api.realestateapi.com/v1/BulkSkipTraceAwait';

interface SkipTraceItem {
  // Property identification
  id?: string;
  address?: string;
  
  // Address parts
  house?: string;
  street?: string;
  city?: string;
  state?: string;
  zip?: string;
  
  // Owner information
  firstName?: string;
  lastName?: string;
  ownerName?: string;
}

interface BulkSkipTraceRequest {
  items: SkipTraceItem[];
  awaitResults?: boolean; // If true, uses BulkSkipTraceAwait endpoint
  includePhoneNumbers?: boolean;
  includeEmails?: boolean;
  includeRelatives?: boolean;
  includePreviousAddresses?: boolean;
  webhookUrl?: string;
}

/**
 * Bulk SkipTrace API
 * 
 * Provides bulk skip tracing services for multiple properties.
 * Can operate in two modes:
 * 1. Async mode (default): Returns a job ID for later retrieval
 * 2. Await mode: Waits for results (up to 300 properties)
 * 
 * @route POST /api/skiptrace-bulk
 */
export async function POST(request: NextRequest) {
  try {
    // Check for API key
    const API_KEY = process.env.REAL_ESTATE_API_KEY;
    if (!API_KEY) {
      console.error('Error: REAL_ESTATE_API_KEY is not set in environment variables.');
      return NextResponse.json(
        { 
          success: false,
          statusCode: 500,
          message: 'API key is not configured.' 
        },
        { status: 500 }
      );
    }
    
    // Parse request body
    let body: BulkSkipTraceRequest;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Invalid JSON in request body'
        },
        { status: 400 }
      );
    }
    
    // Validate request
    if (!body.items || !Array.isArray(body.items) || body.items.length === 0) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Request must include an array of items to skip trace'
        },
        { status: 400 }
      );
    }
    
    // Check limits based on mode
    const awaitMode = body.awaitResults === true;
    const maxItems = awaitMode ? 300 : 10000;
    
    if (body.items.length > maxItems) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: `Maximum ${maxItems} items allowed in ${awaitMode ? 'await' : 'async'} mode`
        },
        { status: 400 }
      );
    }
    
    // Build request parameters
    const bulkParams: Record<string, any> = {
      items: body.items.map(item => {
        const skipTraceItem: Record<string, any> = {};
        
        // Property identification
        if (item.id) {
          skipTraceItem.id = item.id;
        } else if (item.address) {
          skipTraceItem.address = item.address;
        } else {
          // Address parts
          if (item.house) skipTraceItem.house = item.house;
          if (item.street) skipTraceItem.street = item.street;
          if (item.city) skipTraceItem.city = item.city;
          if (item.state) skipTraceItem.state = item.state;
          if (item.zip) skipTraceItem.zip = item.zip;
        }
        
        // Owner information if provided
        if (item.firstName) skipTraceItem.firstName = item.firstName;
        if (item.lastName) skipTraceItem.lastName = item.lastName;
        if (item.ownerName) skipTraceItem.ownerName = item.ownerName;
        
        return skipTraceItem;
      })
    };
    
    // Add options
    bulkParams.includePhoneNumbers = body.includePhoneNumbers !== false;
    bulkParams.includeEmails = body.includeEmails !== false;
    bulkParams.includeRelatives = body.includeRelatives !== false;
    bulkParams.includePreviousAddresses = body.includePreviousAddresses !== false;
    
    // Add webhook URL if provided
    if (body.webhookUrl) {
      bulkParams.webhookUrl = body.webhookUrl;
    }
    
    // Select appropriate endpoint
    const apiUrl = awaitMode ? BULK_SKIPTRACE_AWAIT_API_URL : BULK_SKIPTRACE_API_URL;
    
    // Set up headers
    const headers = {
      'Content-Type': 'application/json',
      'X-API-KEY': API_KEY
    };
    
    console.log(`Executing Bulk SkipTrace in ${awaitMode ? 'await' : 'async'} mode for ${body.items.length} items`);
    
    // Set up request timeout (longer for await mode)
    const controller = new AbortController();
    const timeoutDuration = awaitMode ? 300000 : 60000; // 5 minutes for await, 1 minute for async
    const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);
    
    try {
      // Make request to Bulk SkipTrace API
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(bulkParams),
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      // Check for successful response
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Bulk SkipTrace API error: ${response.status} - ${errorText}`);
        
        return NextResponse.json(
          {
            success: false,
            statusCode: response.status,
            message: `Bulk SkipTrace API error: ${response.statusText}`
          },
          { status: response.status }
        );
      }
      
      // Parse response
      const data = await response.json();
      
      // Return formatted response based on mode
      if (awaitMode) {
        // Await mode returns results directly
        return NextResponse.json({
          success: true,
          mode: 'await',
          data: {
            results: data.results || data.items || [],
            totalProcessed: data.totalProcessed || data.results?.length || 0,
            successCount: data.successCount || 0,
            failureCount: data.failureCount || 0,
            executionTime: data.executionTimeMS || null
          }
        });
      } else {
        // Async mode returns job information
        return NextResponse.json({
          success: true,
          mode: 'async',
          data: {
            jobId: data.jobId || data.job_id,
            status: data.status || 'processing',
            itemCount: body.items.length,
            webhookUrl: body.webhookUrl || null,
            estimatedCompletionTime: data.estimatedCompletionTime || null,
            message: 'Job submitted successfully. Use the job ID to check status and retrieve results.'
          }
        });
      }
      
    } catch (error: any) {
      clearTimeout(timeoutId);
      
      // Handle timeout
      if (error.name === 'AbortError') {
        return NextResponse.json(
          {
            success: false,
            statusCode: 504,
            message: `Bulk SkipTrace API request timed out (${awaitMode ? 'await' : 'async'} mode)`
          },
          { status: 504 }
        );
      }
      
      throw error;
    }
    
  } catch (error: any) {
    console.error('Bulk SkipTrace API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        statusCode: 500,
        message: error.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * Get Bulk SkipTrace Job Status
 * 
 * Retrieves the status and results of a bulk skip trace job.
 * 
 * @route GET /api/skiptrace-bulk?jobId={jobId}
 */
export async function GET(request: NextRequest) {
  try {
    const API_KEY = process.env.REAL_ESTATE_API_KEY;
    if (!API_KEY) {
      return NextResponse.json(
        { 
          success: false,
          statusCode: 500,
          message: 'API key is not configured.' 
        },
        { status: 500 }
      );
    }
    
    // Get job ID from query parameters
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');
    
    if (!jobId) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'jobId parameter is required'
        },
        { status: 400 }
      );
    }
    
    // Note: This endpoint would need to be implemented by RealEstateAPI.com
    // For now, return a placeholder response
    return NextResponse.json({
      success: true,
      message: 'Job status retrieval endpoint not yet implemented by API provider',
      jobId,
      status: 'pending_implementation'
    });
    
  } catch (error: any) {
    console.error('Bulk SkipTrace status error:', error);
    
    return NextResponse.json(
      {
        success: false,
        statusCode: 500,
        message: error.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
} 