import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { customCityList, users, appPreferences } from '@/db/schema';
import { eq } from 'drizzle-orm';

interface CityListItem {
  rank: number;
  city: string;
  state: string;
}

interface UserCityListRequest {
  userId: string;
  cities: CityListItem[];
}

/**
 * GET - Get user's city list (custom or default)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Get user to check their city_state_list preference
    const [user] = await db.select().from(users).where(eq(users.id, userId));
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    let cityList: CityListItem[] = [];

    if (user.cityStateList === 'default') {
      // Get default top 15 cities from app_preferences
      const [preferences] = await db.select().from(appPreferences).where(eq(appPreferences.id, 'default'));
      
      if (preferences) {
        const citiesArray = JSON.parse(preferences.top15CitiesStates);
        // Parse format: ["1", "city", "state", "2", "city", "state", ...]
        for (let i = 0; i < citiesArray.length; i += 3) {
          if (i + 2 < citiesArray.length) {
            cityList.push({
              rank: parseInt(citiesArray[i]),
              city: citiesArray[i + 1],
              state: citiesArray[i + 2]
            });
          }
        }
      }
    } else {
      // Get user's custom city list
      const [customList] = await db.select()
        .from(customCityList)
        .where(eq(customCityList.userId, userId));

      if (customList) {
        const citiesArray = JSON.parse(customList.customCityList);
        // Parse format: ["1", "city", "state", "2", "city", "state", ...]
        for (let i = 0; i < citiesArray.length; i += 3) {
          if (i + 2 < citiesArray.length) {
            cityList.push({
              rank: parseInt(citiesArray[i]),
              city: citiesArray[i + 1],
              state: citiesArray[i + 2]
            });
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      userId,
      isDefault: user.cityStateList === 'default',
      cities: cityList,
      totalCities: cityList.length
    });

  } catch (error) {
    console.error('Error fetching user city list:', error);
    return NextResponse.json({
      error: 'Failed to fetch city list',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * POST - Update user's custom city list
 */
export async function POST(request: NextRequest) {
  try {
    const { userId, cities }: UserCityListRequest = await request.json();

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    if (!cities || !Array.isArray(cities)) {
      return NextResponse.json({ error: 'Cities array is required' }, { status: 400 });
    }

    if (cities.length > 15) {
      return NextResponse.json({ error: 'Maximum 15 cities allowed' }, { status: 400 });
    }

    // Validate city structure
    for (const city of cities) {
      if (!city.city || !city.state || typeof city.rank !== 'number') {
        return NextResponse.json({ 
          error: 'Each city must have city, state, and rank properties' 
        }, { status: 400 });
      }
    }

    // Sort cities by rank and re-rank them
    const sortedCities = cities.sort((a, b) => a.rank - b.rank);
    sortedCities.forEach((city, index) => {
      city.rank = index + 1;
    });

    // Format as required: ["1", "city", "state", "2", "city", "state", ...]
    const formattedArray: string[] = [];
    sortedCities.forEach((city) => {
      formattedArray.push(
        city.rank.toString(),
        city.city,
        city.state
      );
    });

    // Check if user exists
    const [user] = await db.select().from(users).where(eq(users.id, userId));
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Generate custom list ID
    const customListId = `custom_${userId}_${Date.now()}`;

    // Check if user already has a custom list
    const [existingCustomList] = await db.select()
      .from(customCityList)
      .where(eq(customCityList.userId, userId));

    if (existingCustomList) {
      // Update existing custom list
      await db.update(customCityList)
        .set({
          customCityList: JSON.stringify(formattedArray),
          updatedAt: new Date().getTime()
        })
        .where(eq(customCityList.userId, userId));
    } else {
      // Create new custom list
      await db.insert(customCityList).values({
        id: customListId,
        userId,
        customCityList: JSON.stringify(formattedArray),
        createdAt: new Date().getTime(),
        updatedAt: new Date().getTime()
      });
    }

    // Update user's cityStateList to point to custom list
    await db.update(users)
      .set({
        cityStateList: customListId
      })
      .where(eq(users.id, userId));

    return NextResponse.json({
      success: true,
      message: 'City list updated successfully',
      userId,
      customListId,
      cities: sortedCities,
      totalCities: sortedCities.length
    });

  } catch (error) {
    console.error('Error updating user city list:', error);
    return NextResponse.json({
      error: 'Failed to update city list',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * DELETE - Reset user to default city list
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Update user to use default list
    await db.update(users)
      .set({
        cityStateList: 'default'
      })
      .where(eq(users.id, userId));

    // Optionally delete the custom list (or keep for history)
    // await db.delete(customCityList).where(eq(customCityList.userId, userId));

    return NextResponse.json({
      success: true,
      message: 'User reset to default city list',
      userId
    });

  } catch (error) {
    console.error('Error resetting user city list:', error);
    return NextResponse.json({
      error: 'Failed to reset city list',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
