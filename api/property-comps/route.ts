import { NextRequest, NextResponse } from 'next/server';

const PROPERTY_COMPS_V3_API_URL = 'https://api.realestateapi.com/v3/PropertyComps';

interface SubjectProperty {
  // Property identification (one required)
  id?: string;
  address?: string;
  
  // Address parts
  house?: string;
  street?: string;
  city?: string;
  state?: string;
  zip?: string;
  
  // Property characteristics
  bedrooms?: number;
  bathrooms?: number;
  livingSquareFeet?: number;
  lotSquareFeet?: number;
  yearBuilt?: number;
  propertyType?: string;
  
  // Location
  latitude?: number;
  longitude?: number;
}

interface CompsSettings {
  // Search criteria
  radius?: number; // Miles radius for comp search (default: 2)
  saleRecency?: number; // Days back to search for sales (default: 180)
  compCount?: number; // Number of comps to return (default: 10)
  minSimilarity?: number; // Minimum similarity score (0-1, default: 0.7)
  
  // Filters
  propertyTypes?: string[]; // Property types to include
  excludeSubject?: boolean; // Exclude subject property from results
  requirePhotos?: boolean; // Only include comps with photos
  mlsOnly?: boolean; // Only use MLS sales
}

interface Boosts {
  // Boost factors (1.0 = neutral, >1.0 = prioritize, <1.0 = deprioritize)
  saleRecency?: number; // Boost recent sales (default: 1.5)
  proximity?: number; // Boost nearby properties (default: 1.5)
  similarity?: number; // Boost similar properties (default: 1.0)
  squareFootage?: number; // Boost properties with similar square footage
  lotSize?: number; // Boost properties with similar lot size
  age?: number; // Boost properties of similar age
  propertyType?: number; // Boost same property type
}

interface PropertyCompsV3Request {
  subject: SubjectProperty;
  compsSettings?: CompsSettings;
  boosts?: Boosts;
}

/**
 * Property Comps v3 API
 * 
 * Advanced comparable properties analysis with customizable parameters and boosts.
 * Provides detailed valuation insights with adjustments and confidence scoring.
 * 
 * @route POST /api/property-comps
 */
export async function POST(request: NextRequest) {
  try {
    // Check for API key
    const API_KEY = process.env.REAL_ESTATE_API_KEY;
    if (!API_KEY) {
      console.error('Error: REAL_ESTATE_API_KEY is not set in environment variables.');
      return NextResponse.json(
        { 
          success: false,
          statusCode: 500,
          message: 'API key is not configured.' 
        },
        { status: 500 }
      );
    }
    
    // Parse request body
    let body: PropertyCompsV3Request;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Invalid JSON in request body'
        },
        { status: 400 }
      );
    }
    
    // Validate required subject property
    if (!body.subject) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'subject property is required'
        },
        { status: 400 }
      );
    }
    
    // Validate subject property has identification
    if (!body.subject.id && !body.subject.address && !body.subject.house) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'subject property must include either id, address, or address parts (house, street, city, state, zip)'
        },
        { status: 400 }
      );
    }
    
    // Build request parameters
    const compsParams: Record<string, any> = {
      subject: body.subject
    };
    
    // Add comps settings with defaults
    const defaultCompsSettings: CompsSettings = {
      radius: 2,
      saleRecency: 180,
      compCount: 10,
      minSimilarity: 0.7,
      excludeSubject: true,
      mlsOnly: false
    };
    
    compsParams.compsSettings = {
      ...defaultCompsSettings,
      ...(body.compsSettings || {})
    };
    
    // Add boosts with defaults
    const defaultBoosts: Boosts = {
      saleRecency: 1.5,
      proximity: 1.5,
      similarity: 1.0,
      squareFootage: 1.2,
      lotSize: 1.1,
      age: 1.1,
      propertyType: 1.3
    };
    
    compsParams.boosts = {
      ...defaultBoosts,
      ...(body.boosts || {})
    };
    
    // Set up headers
    const headers = {
      'Content-Type': 'application/json',
      'X-API-KEY': API_KEY
    };
    
    console.log('Executing Property Comps v3 for:', body.subject.id || body.subject.address || 'address parts');
    
    // Set up request timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout
    
    try {
      // Make request to Property Comps v3 API
      const response = await fetch(PROPERTY_COMPS_V3_API_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(compsParams),
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      // Check for successful response
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Property Comps v3 API error: ${response.status} - ${errorText}`);
        
        return NextResponse.json(
          {
            success: false,
            statusCode: response.status,
            message: `Property Comps v3 API error: ${response.statusText}`
          },
          { status: response.status }
        );
      }
      
      // Parse response
      const data = await response.json();
      
      // Return formatted response
      return NextResponse.json({
        success: true,
        data: {
          // Subject property information
          subject: {
            id: data.subject?.id || body.subject.id,
            address: data.subject?.address || body.subject.address,
            propertyType: data.subject?.propertyType,
            bedrooms: data.subject?.bedrooms,
            bathrooms: data.subject?.bathrooms,
            livingSquareFeet: data.subject?.livingSquareFeet,
            lotSquareFeet: data.subject?.lotSquareFeet,
            yearBuilt: data.subject?.yearBuilt,
            latitude: data.subject?.latitude,
            longitude: data.subject?.longitude
          },
          
          // Comparable properties with adjustments
          comps: (data.comps || []).map((comp: any) => ({
            id: comp.id,
            address: comp.address,
            distance: comp.distance,
            
            // Property details
            bedrooms: comp.bedrooms,
            bathrooms: comp.bathrooms,
            livingSquareFeet: comp.livingSquareFeet,
            lotSquareFeet: comp.lotSquareFeet,
            yearBuilt: comp.yearBuilt,
            propertyType: comp.propertyType,
            
            // Sale information
            saleDate: comp.saleDate,
            salePrice: comp.salePrice,
            daysOnMarket: comp.daysOnMarket,
            
            // Adjustments and analysis
            adjustments: {
              squareFootageAdjustment: comp.adjustments?.squareFootageAdjustment || 0,
              bedroomAdjustment: comp.adjustments?.bedroomAdjustment || 0,
              bathroomAdjustment: comp.adjustments?.bathroomAdjustment || 0,
              lotSizeAdjustment: comp.adjustments?.lotSizeAdjustment || 0,
              ageAdjustment: comp.adjustments?.ageAdjustment || 0,
              locationAdjustment: comp.adjustments?.locationAdjustment || 0,
              conditionAdjustment: comp.adjustments?.conditionAdjustment || 0,
              totalAdjustment: comp.adjustments?.totalAdjustment || 0
            },
            
            // Calculated values
            adjustedValue: comp.adjustedValue || comp.salePrice,
            pricePerSqFt: comp.pricePerSqFt,
            adjustedPricePerSqFt: comp.adjustedPricePerSqFt,
            similarity: comp.similarity,
            weight: comp.weight,
            
            // Quality indicators
            photoCount: comp.photoCount,
            dataSource: comp.dataSource,
            mlsNumber: comp.mlsNumber
          })),
          
          // Valuation summary
          valuation: {
            estimatedValue: data.estimatedValue,
            lowValue: data.lowValue,
            highValue: data.highValue,
            confidenceScore: data.confidenceScore,
            pricePerSqFt: data.pricePerSqFt,
            
            // Methodology
            methodology: data.methodology || 'weighted_average',
            adjustmentsMade: data.adjustmentsMade || true,
            compsUsed: data.compsUsed || (data.comps || []).length,
            
            // Market context
            marketTrend: data.marketTrend,
            marketCondition: data.marketCondition,
            marketVelocity: data.marketVelocity
          },
          
          // Search parameters used
          searchCriteria: {
            radius: compsParams.compsSettings.radius,
            saleRecency: compsParams.compsSettings.saleRecency,
            compCount: compsParams.compsSettings.compCount,
            minSimilarity: compsParams.compsSettings.minSimilarity,
            boosts: compsParams.boosts
          },
          
          // Quality metrics
          dataQuality: {
            totalCompsFound: data.totalCompsFound,
            compsUsed: (data.comps || []).length,
            averageSimilarity: data.averageSimilarity,
            averageDistance: data.averageDistance,
            averageDaysOld: data.averageDaysOld,
            mlsPercentage: data.mlsPercentage
          },
          
          // Raw response for debugging
          _raw: data
        },
        executionTime: data.executionTimeMS || null
      });
      
    } catch (error: any) {
      clearTimeout(timeoutId);
      
      // Handle timeout
      if (error.name === 'AbortError') {
        return NextResponse.json(
          {
            success: false,
            statusCode: 504,
            message: 'Property Comps v3 API request timed out'
          },
          { status: 504 }
        );
      }
      
      throw error;
    }
    
  } catch (error: any) {
    console.error('Property Comps v3 API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        statusCode: 500,
        message: error.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}