import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { propertyViewHistory } from '@/db/schema';
import { eq, desc, and } from 'drizzle-orm';

interface PropertyViewRequest {
  userId?: string;
  propertyId: string;
  profileId?: string;
}

/**
 * POST - Track property view
 */
export async function POST(request: NextRequest) {
  try {
    const {
      userId,
      propertyId,
      profileId
    }: PropertyViewRequest = await request.json();

    if (!propertyId) {
      return NextResponse.json({ 
        error: 'Property ID is required' 
      }, { status: 400 });
    }

    const now = new Date().getTime();

    // Check if this property view already exists for this user
    let existingView = null;
    
    if (userId) {
      [existingView] = await db.select()
        .from(propertyViewHistory)
        .where(
          and(
            eq(propertyViewHistory.userId, userId),
            eq(propertyViewHistory.propertyId, propertyId)
          )
        );
    } else {
      // For anonymous users, check by property ID only in recent views
      [existingView] = await db.select()
        .from(propertyViewHistory)
        .where(
          and(
            eq(propertyViewHistory.propertyId, propertyId),
            eq(propertyViewHistory.userId, null)
          )
        )
        .orderBy(desc(propertyViewHistory.lastViewedAt))
        .limit(1);
    }

    if (existingView) {
      // Update existing view - increment count and update timestamp
      await db.update(propertyViewHistory)
        .set({
          viewCount: existingView.viewCount + 1,
          lastViewedAt: now,
          profileId: profileId || existingView.profileId
        })
        .where(eq(propertyViewHistory.id, existingView.id));

      return NextResponse.json({
        success: true,
        action: 'updated',
        viewId: existingView.id,
        viewCount: existingView.viewCount + 1,
        propertyId
      });
    } else {
      // Create new view record
      const viewId = `view_${propertyId}_${Date.now()}`;
      
      await db.insert(propertyViewHistory).values({
        id: viewId,
        userId: userId || null,
        propertyId,
        profileId,
        viewCount: 1,
        firstViewedAt: now,
        lastViewedAt: now
      });

      return NextResponse.json({
        success: true,
        action: 'created',
        viewId,
        viewCount: 1,
        propertyId
      });
    }

  } catch (error) {
    console.error('Error tracking property view:', error);
    return NextResponse.json({
      error: 'Failed to track property view',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * GET - Retrieve property view history
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const propertyId = searchParams.get('propertyId');
    const limit = parseInt(searchParams.get('limit') || '50');

    if (!userId && !propertyId) {
      return NextResponse.json({ 
        error: 'Either User ID or Property ID is required' 
      }, { status: 400 });
    }

    let query = db.select().from(propertyViewHistory);

    if (userId && propertyId) {
      // Get specific property view for specific user
      query = query.where(
        and(
          eq(propertyViewHistory.userId, userId),
          eq(propertyViewHistory.propertyId, propertyId)
        )
      );
    } else if (userId) {
      // Get all property views for a user
      query = query.where(eq(propertyViewHistory.userId, userId));
    } else if (propertyId) {
      // Get all views for a property (across all users)
      query = query.where(eq(propertyViewHistory.propertyId, propertyId));
    }

    const viewHistory = await query
      .orderBy(desc(propertyViewHistory.lastViewedAt))
      .limit(Math.min(limit, 200));

    const formattedHistory = viewHistory.map(view => ({
      id: view.id,
      userId: view.userId,
      propertyId: view.propertyId,
      profileId: view.profileId,
      viewCount: view.viewCount,
      firstViewedAt: new Date(view.firstViewedAt).toISOString(),
      lastViewedAt: new Date(view.lastViewedAt).toISOString()
    }));

    // Calculate summary stats
    const totalViews = formattedHistory.reduce((sum, view) => sum + view.viewCount, 0);
    const uniqueProperties = new Set(formattedHistory.map(view => view.propertyId)).size;
    const uniqueUsers = new Set(formattedHistory.map(view => view.userId).filter(Boolean)).size;

    return NextResponse.json({
      success: true,
      views: formattedHistory,
      summary: {
        totalRecords: formattedHistory.length,
        totalViews,
        uniqueProperties,
        uniqueUsers
      }
    });

  } catch (error) {
    console.error('Error fetching property view history:', error);
    return NextResponse.json({
      error: 'Failed to fetch property view history',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
