import { NextRequest, NextResponse } from 'next/server';
import { verifyAddress } from '@/app/lib/realestateapi';

export async function POST(request: NextRequest) {
  try {
    const { address } = await request.json();

    if (!address) {
      return NextResponse.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    const result = await verifyAddress(address);
    
    if (!result) {
      return NextResponse.json(
        { error: 'Failed to verify address' },
        { status: 500 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Address verification API error:', error);
    return NextResponse.json(
      { error: 'Failed to verify address' },
      { status: 500 }
    );
  }
}