import { NextRequest, NextResponse } from 'next/server';
import { getPropertyDetails, getPropertyDetailsByAddress } from '@/app/lib/realestateapi';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Property details request:', body);

    let propertyData = null;

    // Try to get property details by ID first, then by address
    if (body.propertyId) {
      propertyData = await getPropertyDetails(body.propertyId);
    } else if (body.address) {
      propertyData = await getPropertyDetailsByAddress(body.address);
    } else {
      return NextResponse.json(
        { error: 'Either propertyId or address is required' },
        { status: 400 }
      );
    }

    if (!propertyData) {
      return NextResponse.json(
        { error: 'Property details not found' },
        { status: 404 }
      );
    }

    // Extract the actual property data from the API response
    const data = propertyData.data || propertyData;

    // Map the Real Estate API response to our PropertyDetails format
    const mappedPropertyDetails = {
      address: body.address || {
        street: data.propertyInfo?.address?.address || '',
        city: data.propertyInfo?.address?.city || '',
        state: data.propertyInfo?.address?.state || '',
        zipCode: data.propertyInfo?.address?.zip || '',
        fullAddress: data.propertyInfo?.address?.address || `${data.propertyInfo?.address?.address}, ${data.propertyInfo?.address?.city}, ${data.propertyInfo?.address?.state} ${data.propertyInfo?.address?.zip}`,
      },
      propertyId: data.id?.toString(),
      apn: data.lotInfo?.apn,
      county: data.propertyInfo?.address?.county,
      latitude: data.propertyInfo?.latitude,
      longitude: data.propertyInfo?.longitude,
      propertyType: data.propertyType,
      vacant: data.vacant,
      absenteeOwner: data.absenteeOwner,
      ownerOccupied: data.ownerOccupied,

      // Property characteristics
      bedrooms: data.propertyInfo?.bedrooms,
      bathrooms: data.propertyInfo?.bathrooms,
      squareFeet: data.propertyInfo?.buildingSquareFeet || data.propertyInfo?.livingSquareFeet,
      lotSize: data.propertyInfo?.lotSquareFeet,
      lotSquareFeet: data.propertyInfo?.lotSquareFeet,
      acres: parseFloat(data.lotInfo?.lotAcres) || undefined,
      yearBuilt: data.propertyInfo?.yearBuilt,
      stories: data.propertyInfo?.stories,
      rooms: data.propertyInfo?.roomsCount,
      units: data.propertyInfo?.unitsCount,
      fireplace: data.propertyInfo?.fireplace,
      pool: data.propertyInfo?.pool,
      garageType: data.propertyInfo?.garageType,
      heatingType: data.propertyInfo?.heatingType,
      airConditioningType: data.propertyInfo?.airConditioningType,
      roofMaterial: data.propertyInfo?.roofMaterial,
      exteriorWalls: data.propertyInfo?.construction,

      // Legal and lot information
      legalDescription: data.lotInfo?.legalDescription,
      subdivision: data.lotInfo?.subdivision,
      zoning: data.lotInfo?.zoning,
      landUse: data.lotInfo?.landUse,

      // Owner information
      ownerName: data.ownerInfo ?
        `${data.ownerInfo.owner1FirstName || ''} ${data.ownerInfo.owner1LastName || ''}`.trim() : undefined,
      ownerType: data.ownerInfo?.owner1Type,
      yearsOwned: data.ownerInfo?.ownershipLength,

      // Financial information
      estimatedValue: data.taxInfo?.marketValue || data.estimatedValue,
      assessedValue: data.taxInfo?.assessedValue,
      taxAmount: parseFloat(data.taxInfo?.taxAmount) || undefined,
    };

    console.log('Mapped property details:', mappedPropertyDetails);

    return NextResponse.json({
      success: true,
      propertyDetails: mappedPropertyDetails,
      rawData: propertyData // Include raw data for debugging
    });

  } catch (error) {
    console.error('Property details API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch property details', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
