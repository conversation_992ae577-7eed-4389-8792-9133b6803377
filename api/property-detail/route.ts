import { NextRequest, NextResponse } from 'next/server';
import { FmrData } from '../../types/hud';

const PROPERTY_DETAIL_URL = 'https://api.realestateapi.com/v2/PropertyDetail';
const PROPERTY_COMPS_URL = 'https://api.realestateapi.com/v3/PropertyComps';
const HUD_API_URL = '/api/hud/fmr';

/**
 * CMS Property Detail API
 * 
 * Fetches comprehensive property information including:
 * - Basic property details
 * - Owner information
 * - Tax assessment data
 * - Sale history
 * - Mortgage information
 * - MLS history
 * - Demographics
 * - Schools
 * - Comparable properties
 * - Section 8 metrics from HUD Fair Market Rent data
 * 
 * @route POST /api/property-detail
 */
export async function POST(request: NextRequest) {
  try {
    // Check for API key
    const API_KEY = process.env.REAL_ESTATE_API_KEY;
    if (!API_KEY) {
      console.error('Error: REAL_ESTATE_API_KEY is not set in environment variables.');
      return NextResponse.json(
        { 
          success: false,
          statusCode: 500,
          message: 'API key is not configured.' 
        },
        { status: 500 }
      );
    }
    
    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Invalid JSON in request body'
        },
        { status: 400 }
      );
    }
    
    // Validate request parameters
    if (!body.id && !body.address && !body.house) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Request must include either a property ID, full address, or address parts (house, street, city, state, zip)'
        },
        { status: 400 }
      );
    }
    
    // Set up request parameters for Property Detail
    const detailParams: Record<string, any> = {};
    
    if (body.id) {
      detailParams.id = body.id;
    } else if (body.address) {
      detailParams.address = body.address;
    } else if (body.house) {
      // Address parts search
      detailParams.house = body.house;
      detailParams.street = body.street;
      detailParams.city = body.city;
      detailParams.state = body.state;
      detailParams.zip = body.zip;
      
      // Validate required fields for address parts search
      if (!detailParams.state || detailParams.state.length !== 2) {
        return NextResponse.json(
          {
            success: false,
            statusCode: 400,
            message: 'State must be a 2-character abbreviation'
          },
          { status: 400 }
        );
      }
      
      if (!detailParams.zip || detailParams.zip.length !== 5) {
        return NextResponse.json(
          {
            success: false,
            statusCode: 400,
            message: 'Zip code must be 5 characters long'
          },
          { status: 400 }
        );
      }
    }
    
    // Set up request headers
    const headers = {
      'Content-Type': 'application/json',
      'X-API-KEY': API_KEY
    };
    
    console.log(`Fetching property details for ${body.id ? `ID: ${body.id}` : body.address ? `address: ${body.address}` : 'address parts'}`);
    
    // Set up request timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout
    
    // Make request to Property Detail API
    const detailResponse = await fetch(PROPERTY_DETAIL_URL, {
      method: 'POST',
      headers,
      body: JSON.stringify(detailParams),
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    // Check for successful response
    if (!detailResponse.ok) {
      let errorMessage = `API error: ${detailResponse.status} ${detailResponse.statusText}`;
      
      try {
        const errorData = await detailResponse.json();
        
        // Handle specific error cases
        if (errorData.reason === 'Street is not similar to found properties') {
          errorMessage = 'Property not found. Please verify the address is correct.';
        } else if (errorData.message) {
          errorMessage = errorData.message;
        }
        
        // Handle validation errors
        if (errorData.validation) {
          errorMessage = errorData.message || 'Validation error';
        }
      } catch {
        // If we can't parse the error response, use the default message
      }
      
      // Handle 404s specially
      if (detailResponse.status === 404) {
        return NextResponse.json(
          {
            success: false,
            statusCode: 404,
            message: 'Property not found'
          },
          { status: 404 }
        );
      }
      
      console.error(errorMessage);
      
      return NextResponse.json(
        {
          success: false,
          statusCode: detailResponse.status,
          message: errorMessage
        },
        { status: detailResponse.status }
      );
    }
    
    // Parse property detail response
    const detailData = await detailResponse.json();
    
    // Extract property data from the response
    const propertyData = detailData.data || detailData;
    
    // Fetch comparable properties if requested (default to true)
    let compsData = null;
    const includeComps = body.includeComps !== false;
    
    if (includeComps && propertyData) {
      try {
        // Prepare subject property for comps request
        const subjectProperty: Record<string, any> = {
          id: propertyData.id || propertyData.propertyId
        };
        
        // Add property characteristics for better comp matching
        if (propertyData.propertyInfo) {
          if (propertyData.propertyInfo.bedrooms) subjectProperty.bedrooms = propertyData.propertyInfo.bedrooms;
          if (propertyData.propertyInfo.bathrooms) subjectProperty.bathrooms = propertyData.propertyInfo.bathrooms;
          if (propertyData.propertyInfo.livingSquareFeet) subjectProperty.livingSquareFeet = propertyData.propertyInfo.livingSquareFeet;
          if (propertyData.propertyInfo.yearBuilt) subjectProperty.yearBuilt = propertyData.propertyInfo.yearBuilt;
          if (propertyData.propertyInfo.propertyType) subjectProperty.propertyType = propertyData.propertyInfo.propertyType;
        }
        
        // Add location data for proximity matching
        if (propertyData.location) {
          if (propertyData.location.latitude) subjectProperty.latitude = propertyData.location.latitude;
          if (propertyData.location.longitude) subjectProperty.longitude = propertyData.location.longitude;
        }
        
        // Set up comps request with custom parameters
        const compsParams = {
          subject: subjectProperty,
          // Custom comp criteria
          radius: body.compsRadius || 2, // Default 2 mile radius
          saleRecency: body.compsSaleRecency || 180, // Default 180 days
          compCount: body.compsCount || 10, // Default 10 comps
          // Boosts for better matching
          boosts: {
            saleRecency: 2.0, // Prioritize recent sales
            proximity: 1.5, // Prioritize nearby properties
            similarity: 1.5 // Prioritize similar properties
          },
          // Filters
          minSimilarity: 0.7, // At least 70% similarity
          excludeSubject: true // Don't include the subject property
        };
        
        // Make request to Comps API
        const compsController = new AbortController();
        const compsTimeoutId = setTimeout(() => compsController.abort(), 15000); // 15 seconds timeout
        
        const compsResponse = await fetch(PROPERTY_COMPS_URL, {
          method: 'POST',
          headers,
          body: JSON.stringify(compsParams),
          signal: compsController.signal
        });
        
        clearTimeout(compsTimeoutId);
        
        if (compsResponse.ok) {
          compsData = await compsResponse.json();
        } else {
          console.error('Failed to fetch comparable properties:', compsResponse.status);
        }
      } catch (error) {
        console.error('Error fetching comparable properties:', error);
        // Continue without comps data
      }
    }
    
    // Calculate Section 8 metrics if property has the necessary data
    let section8Metrics = null;
    
    if (propertyData.propertyInfo && propertyData.propertyInfo.bedrooms) {
      const bedrooms = propertyData.propertyInfo.bedrooms;
      
      // Get suggested rent from various sources
      let suggestedRent = null;
      if (propertyData.demographics && propertyData.demographics.rentalEstimates) {
        suggestedRent = propertyData.demographics.rentalEstimates.rentEstimate;
      } else if (propertyData.suggestedRent) {
        suggestedRent = parseFloat(propertyData.suggestedRent);
      }
      
      let zipCode = null;
      
      // Try to get zip code from various property data sources
      if (propertyData.address && propertyData.address.zipCode) {
        zipCode = propertyData.address.zipCode;
      } else if (propertyData.address && propertyData.address.zip) {
        zipCode = propertyData.address.zip;
      } else if (propertyData.lotInfo && propertyData.lotInfo.zipCode) {
        zipCode = propertyData.lotInfo.zipCode;
      }
      
      if (suggestedRent && zipCode) {
        try {
          // Call HUD API to get FMR data based on zip code
          const hudResponse = await fetch(`${HUD_API_URL}?zip=${zipCode}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            }
          });
          
          if (hudResponse.ok) {
            const hudData = await hudResponse.json();
            
            if (hudData.success && hudData.data && hudData.data.length > 0) {
              const fmrData: FmrData = hudData.data[0];
              let fairMarketRent = 0;
              
              // Map bedrooms to the appropriate FMR field
              switch (bedrooms) {
                case 0: // Studio
                  fairMarketRent = fmrData.fmr0;
                  break;
                case 1:
                  fairMarketRent = fmrData.fmr1;
                  break;
                case 2:
                  fairMarketRent = fmrData.fmr2;
                  break;
                case 3:
                  fairMarketRent = fmrData.fmr3;
                  break;
                case 4:
                  fairMarketRent = fmrData.fmr4;
                  break;
                default:
                  // For 5+ bedrooms, use fmr4 as the base and add 15% per additional bedroom
                  if (bedrooms > 4) {
                    fairMarketRent = fmrData.fmr4 * (1 + 0.15 * (bedrooms - 4));
                  } else {
                    fairMarketRent = fmrData.fmr2; // Fallback
                  }
              }
              
              section8Metrics = {
                bedrooms,
                suggestedRent,
                fairMarketRent,
                eligible: suggestedRent <= fairMarketRent,
                rentRatio: suggestedRent / fairMarketRent,
                fmrYear: fmrData.year,
                areaName: fmrData.hud_area_name,
                zipCode: fmrData.zip_code,
                hudDesignation: propertyData.demographics?.hudDesignation || null
              };
            }
          } else {
            console.error('Failed to fetch HUD FMR data, using fallback');
            section8Metrics = createFallbackSection8Metrics(bedrooms, suggestedRent);
          }
        } catch (error) {
          console.error('Error fetching HUD FMR data:', error);
          section8Metrics = createFallbackSection8Metrics(bedrooms, suggestedRent);
        }
      } else if (suggestedRent) {
        // If we have rent but no zip code, use fallback
        section8Metrics = createFallbackSection8Metrics(bedrooms, suggestedRent);
      }
    }
    
    // Build comprehensive response including all available data
    const response = {
      success: true,
      data: {
        // Basic property information
        id: propertyData.id || propertyData.propertyId,
        propertyId: propertyData.propertyId || propertyData.id,
        address: propertyData.address,
        
        // Property characteristics
        propertyInfo: propertyData.propertyInfo,
        
        // Lot and location information
        lotInfo: propertyData.lotInfo,
        location: propertyData.location,
        
        // Owner information
        ownerInfo: propertyData.ownerInfo,
        
        // Financial information
        taxInfo: propertyData.taxInfo,
        assessedValue: propertyData.assessedValue,
        assessedLandValue: propertyData.assessedLandValue,
        assessedImprovementValue: propertyData.assessedImprovementValue,
        estimatedValue: propertyData.estimatedValue,
        estimatedRent: propertyData.estimatedRent,
        suggestedRent: propertyData.suggestedRent,
        
        // Sale and mortgage history
        saleHistory: propertyData.saleHistory,
        lastSaleDate: propertyData.lastSaleDate,
        lastSaleAmount: propertyData.lastSaleAmount,
        currentMortgages: propertyData.currentMortgages,
        mortgageHistory: propertyData.mortgageHistory,
        
        // MLS information
        mlsHistory: propertyData.mlsHistory,
        mlsActive: propertyData.mlsActive,
        mlsListingPrice: propertyData.mlsListingPrice,
        mlsDaysOnMarket: propertyData.mlsDaysOnMarket,
        mlsStatus: propertyData.mlsStatus,
        
        // Auction information
        auctionInfo: propertyData.auctionInfo,
        
        // Demographics and neighborhood
        demographics: propertyData.demographics,
        neighborhood: propertyData.neighborhood,
        
        // Schools
        schools: propertyData.schools,
        
        // Property features
        features: propertyData.features,
        amenities: propertyData.amenities,
        
        // Linked properties (for multi-unit or portfolio)
        linkedProperties: propertyData.linkedProperties,
        
        // Additional data fields that may be present
        propertyType: propertyData.propertyType,
        yearBuilt: propertyData.yearBuilt,
        bedrooms: propertyData.bedrooms,
        bathrooms: propertyData.bathrooms,
        squareFeet: propertyData.squareFeet,
        lotSize: propertyData.lotSize,
        
        // Flags and indicators
        absenteeOwner: propertyData.absenteeOwner,
        corporateOwned: propertyData.corporateOwned,
        cashBuyer: propertyData.cashBuyer,
        distressed: propertyData.distressed,
        foreclosure: propertyData.foreclosure,
        preForeclosure: propertyData.preForeclosure,
        vacant: propertyData.vacant,
        highEquity: propertyData.highEquity,
        
        // Section 8 specific metrics
        section8Metrics,
        
        // Comparable properties
        comparables: compsData,
        
        // Include original comps if it was part of detail request (backward compatibility)
        comps: propertyData.comps
      },
      // Include execution time metrics
      executionTime: {
        propertyDetail: detailData.requestExecutionTimeMS || null,
        comps: compsData?.executionTimeMS || null
      },
      // Include raw response metadata if available
      metadata: {
        live: detailData.live,
        statusCode: detailData.statusCode,
        statusMessage: detailData.statusMessage
      }
    };
    
    return NextResponse.json(response);
    
  } catch (error: any) {
    // Handle request timeout
    if (error.name === 'AbortError') {
      return NextResponse.json(
        {
          success: false,
          statusCode: 504,
          message: 'Property detail API request timed out'
        },
        { status: 504 }
      );
    }
    
    // Handle other errors
    console.error('Property detail API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        statusCode: 500,
        message: error.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * Creates fallback Section 8 metrics when HUD API is unavailable
 */
function createFallbackSection8Metrics(bedrooms: number, suggestedRent: number) {
  const nationalAvgFmr = getNationalAverageFmr(bedrooms);
  
  return {
    bedrooms,
    suggestedRent,
    fairMarketRent: nationalAvgFmr,
    eligible: suggestedRent <= nationalAvgFmr,
    rentRatio: suggestedRent / nationalAvgFmr,
    isEstimate: true,
    estimateType: 'national average'
  };
}

/**
 * Returns national average Fair Market Rent based on bedroom count
 * Data based on HUD's national FMR statistics (approximate 2024 values)
 * Used as fallback when HUD API data is not available
 */
function getNationalAverageFmr(bedrooms: number): number {
  // National average FMR values by bedroom count (based on 2024 HUD data)
  switch (bedrooms) {
    case 0: // Studio
      return 1100;
    case 1:
      return 1200;
    case 2:
      return 1435; // National average mentioned in the documentation
    case 3:
      return 1980;
    case 4:
      return 2400;
    default:
      // For 5+ bedrooms, add 15% for each additional bedroom (HUD guideline)
      if (bedrooms > 4) {
        return Math.round(2400 * (1 + (bedrooms - 4) * 0.15));
      }
      // Default to 2 bedroom rate if invalid bedroom count
      return 1435;
  }
}