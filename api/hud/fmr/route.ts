import { NextRequest, NextResponse } from 'next/server';

interface FmrData {
  fmr0?: number;
  fmr1?: number;
  fmr2?: number;
  fmr3?: number;
  fmr4?: number;
  year?: string;
  hud_area_name?: string;
  metro_area_name?: string;
  smallarea_name?: string;
  county_name?: string;
  state_alpha?: string;
  zip_code?: string;
}

interface HudFmrResponse {
  fmr0?: number;
  fmr1?: number;
  fmr2?: number;
  fmr3?: number;
  fmr4?: number;
  year?: string;
  hud_area_name?: string;
  metro_area_name?: string;
  smallarea_name?: string;
  county_name?: string;
  state_alpha?: string;
  zip_code?: string;
  areaname?: string;
  geography_name?: string;
  countyname?: string;
}

interface TopCityResponse {
  city: string;
  state: string;
  fmrRatio: number;
  vouchers: string;
  avgFmr?: number;
  data?: HudFmrResponse[];
}

interface HudFmrTopCitiesRequest {
  city?: string;
  state?: string;
}

interface BulkLocationRequest {
  city?: string;
  state?: string;
  zip?: string;
  countyname?: string;
}

/**
 * HUD Fair Market Rent (FMR) API
 * 
 * Fetches real-time FMR data from HUD API with support for different query types.
 * All data is retrieved from official HUD sources - no mock data.
 * 
 * @route GET /api/hud/fmr
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    
    // Check for different request types
    const hudPath = searchParams.get('hudPath');
    const endpoint = searchParams.get('endpoint');
    
    // Location parameters
    const zip = searchParams.get('zip');
    const year = searchParams.get('year') || new Date().getFullYear().toString();
    const countyname = searchParams.get('countyname');
    const state = searchParams.get('state');
    const city = searchParams.get('city');

    // Handle different request types based on hudPath or endpoint
    const requestType = hudPath || endpoint;
    
    switch (requestType) {
      case 'topSection8Cities':
      case 'top-cities':
        return await handleTopCitiesRequest({ city: city || undefined, state: state || undefined }, year);

      case 'cityMetrics':
      case 'city-metrics':
        if (!city || !state) {
          return NextResponse.json(
            {
              success: false,
              statusCode: 400,
              message: 'city and state parameters required for cityMetrics'
            },
            { status: 400 }
          );
        }
        return await fetchHudFmrData(city, state, zip, year);

      case 'marketAnalysis':
      case 'market-analysis':
        if (!state) {
          return NextResponse.json(
            {
              success: false,
              statusCode: 400,
              message: 'state parameter required for market analysis'
            },
            { status: 400 }
          );
        }
        return await handleMarketAnalysisRequest(state, year);

      default:
        // Default behavior - fetch from HUD API
        return await fetchHudFmrData(city, state, zip, year, countyname);
    }

  } catch (error: any) {
    console.error('HUD FMR API error:', error);
    return NextResponse.json(
      {
        success: false,
        statusCode: 500,
        message: error.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * POST endpoint for bulk FMR requests
 */
export async function POST(request: NextRequest) {
  try {
    let body: any = {};
    
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Invalid JSON body'
        },
        { status: 400 }
      );
    }

    // Check if this is a bulk locations request
    if (body.locations && Array.isArray(body.locations)) {
      return await handleBulkFmrRequest(body);
    }

    // Handle single location request
    const { city, state, zip, year, countyname } = body;
    return await fetchHudFmrData(city, state, zip, year, countyname);

  } catch (error: any) {
    console.error('HUD FMR POST error:', error);
    return NextResponse.json(
      {
        success: false,
        statusCode: 500,
        message: error.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * Handle top cities requests - fetches FMR data for major metro areas
 */
async function handleTopCitiesRequest(params: HudFmrTopCitiesRequest, year: string): Promise<NextResponse> {
  const { city, state } = params;

  try {
    // If specific city and state are provided, fetch that data
    if (city && state) {
      const cityData = await fetchHudFmrData(city, state, undefined, year);
      return cityData;
    }

    // If only state is provided, get state-wide data
    if (state) {
      const stateData = await fetchStateWideData(state, year);
      return stateData;
    }

    // For general top cities request, get data for major metro areas
    const majorMetroAreas = [
      'METRO12700M12700', // Atlanta-Sandy Springs-Alpharetta, GA HUD Metro FMR Area
      'METRO19100M19100', // Dallas-Plano-Irving, TX HUD Metro FMR Area
      'METRO26420M26420', // Houston-The Woodlands-Sugar Land, TX HUD Metro FMR Area
      'METRO38060M38060', // Phoenix-Mesa-Chandler, AZ HUD Metro FMR Area
      'METRO17140M17140', // Cincinnati, OH-KY-IN HUD Metro FMR Area
      'METRO18140M18140', // Columbus, OH HUD Metro FMR Area
      'METRO17460M17460', // Cleveland-Elyria, OH HUD Metro FMR Area
      'METRO23540M23540', // Hartford-East Hartford-Middletown, CT HUD Metro FMR Area
    ];

    const results: TopCityResponse[] = [];
    
    for (const metroCode of majorMetroAreas) {
      try {
        const metroData = await fetchHudMetroData(metroCode, year);
        if (metroData) {
          results.push(metroData);
        }
      } catch (error) {
        console.warn(`Failed to fetch data for metro ${metroCode}:`, error);
      }
    }

    // Sort by average FMR (2-bedroom as proxy)
    results.sort((a, b) => (b.avgFmr || 0) - (a.avgFmr || 0));

    return NextResponse.json({
      success: true,
      data: results,
      meta: {
        totalCities: results.length,
        requestedCity: city,
        requestedState: state,
        dataSource: 'HUD_API_LIVE',
        lastUpdated: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error('Error processing top cities request:', error);
    return NextResponse.json(
      {
        success: false,
        statusCode: 500,
        message: error.message || 'Failed to fetch top Section 8 cities'
      },
      { status: 500 }
    );
  }
}

/**
 * Handle bulk FMR requests
 */
async function handleBulkFmrRequest(body: any): Promise<NextResponse> {
  const { locations } = body;
  const year = body.year || new Date().getFullYear().toString();

  if (!Array.isArray(locations) || locations.length === 0) {
    return NextResponse.json(
      {
        success: false,
        statusCode: 400,
        message: 'locations array is required'
      },
      { status: 400 }
    );
  }

  const results = [];
  
  for (const location of locations) {
    const { city, state, zip, countyname } = location;
    
    try {
      const hudResponse = await fetchHudFmrData(city, state, zip, year, countyname);
      const hudData = await hudResponse.json();
      
      results.push({
        location,
        data: hudData.success ? hudData.data : null,
        source: 'HUD_API',
        success: hudData.success,
        error: hudData.success ? null : hudData.message
      });
    } catch (error: any) {
      results.push({
        location,
        data: null,
        source: 'HUD_API',
        success: false,
        error: error.message || 'Failed to fetch HUD data'
      });
    }
  }

  return NextResponse.json({
    success: true,
    data: results,
    meta: {
      requestedCount: locations.length,
      successCount: results.filter(r => r.success).length,
      failureCount: results.filter(r => !r.success).length
    }
  });
}

/**
 * Fetch data from HUD API (original hud/fmr functionality + fmr-data fallback)
 */
async function fetchHudFmrData(
  city?: string | null, 
  state?: string | null, 
  zip?: string | null,
  year?: string,
  countyname?: string | null
): Promise<NextResponse> {
  try {
    const hudApiKey = process.env.NEXT_HUD_API_KEY;

    if (!hudApiKey) {
      console.error("HUD API Key not configured.");
      return NextResponse.json(
        { 
          success: false, 
          statusCode: 500,
          message: "Server configuration error: HUD API key missing." 
        },
        { status: 500 }
      );
    }

    if (!zip && !countyname && !state && !city) {
      return NextResponse.json(
        { 
          success: false, 
          statusCode: 400,
          message: "Missing required query parameters. Provide zip, city/state, countyname/state, or state." 
        },
        { status: 400 }
      );
    }

    const BASE_URL = 'https://www.huduser.gov/hudapi/public/fmr/list';
    const params = new URLSearchParams();
    params.append('year', year || new Date().getFullYear().toString());

    if (zip) {
      params.append('zip_code', zip);
    } else if (countyname && state) {
      params.append('countyname', countyname);
      params.append('state', state);
    } else if (city && state) {
      params.append('countyname', city);
      params.append('state', state);
    } else if (state) {
      params.append('state', state);
    } else {
      throw new Error('Insufficient location parameters');
    }

    // Set up request timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 seconds timeout

    const hudResponse = await fetch(`${BASE_URL}?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${hudApiKey}`,
        'Content-Type': 'application/json'
      },
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!hudResponse.ok) {
      const errorData = await hudResponse.json().catch(() => ({}));
      console.error(`HUD API Error (${hudResponse.status}):`, errorData);
      
      return NextResponse.json(
        {
          success: false,
          statusCode: hudResponse.status,
          message: `Failed to fetch FMR data from HUD API. Status: ${hudResponse.status}`,
          details: errorData
        },
        { status: hudResponse.status }
      );
    }

    const data = await hudResponse.json();

    if (data && data.data && Array.isArray(data.data.results)) {
      const fmrResults: FmrData[] = data.data.results.map((item: any) => ({
        fmr0: item.fmr0,
        fmr1: item.fmr1,
        fmr2: item.fmr2,
        fmr3: item.fmr3,
        fmr4: item.fmr4,
        year: item.year,
        hud_area_name: item.hud_area_name || item.areaname || item.geography_name,
        metro_area_name: item.metro_area_name,
        smallarea_name: item.smallarea_name,
        county_name: item.countyname,
        state_alpha: item.state_alpha,
        zip_code: item.zip_code,
      }));
      
      return NextResponse.json({
        success: true,
        data: fmrResults,
        source: 'HUD_API'
      });
    } else {
      console.error("Unexpected HUD API response structure:", data);
      
      return NextResponse.json(
        { 
          success: false, 
          statusCode: 500,
          message: "Unexpected response structure from HUD API." 
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    // Handle request timeout
    if (error.name === 'AbortError') {
      return NextResponse.json(
        {
          success: false,
          statusCode: 504,
          message: 'HUD API request timed out'
        },
        { status: 504 }
      );
    }
    
    console.error("Error fetching FMR data:", error);
    
    return NextResponse.json(
      { 
        success: false, 
        statusCode: 500,
        message: error.message || "Internal server error" 
      },
      { status: 500 }
    );
  }
}

/**
 * Fetch state-wide FMR data from HUD API
 */
async function fetchStateWideData(state: string, year: string): Promise<NextResponse> {
  try {
    const hudApiKey = process.env.NEXT_HUD_API_KEY;

    if (!hudApiKey) {
      throw new Error("HUD API Key not configured.");
    }

    const BASE_URL = 'https://www.huduser.gov/hudapi/public/fmr/list';
    const params = new URLSearchParams();
    params.append('year', year);
    params.append('state', state);

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000);

    const hudResponse = await fetch(`${BASE_URL}?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${hudApiKey}`,
        'Content-Type': 'application/json'
      },
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!hudResponse.ok) {
      throw new Error(`HUD API Error: ${hudResponse.status}`);
    }

    const data = await hudResponse.json();
    
    if (data && data.data && Array.isArray(data.data.results)) {
      const fmrResults: FmrData[] = data.data.results.map((item: any) => ({
        fmr0: item.fmr0,
        fmr1: item.fmr1,
        fmr2: item.fmr2,
        fmr3: item.fmr3,
        fmr4: item.fmr4,
        year: item.year,
        hud_area_name: item.hud_area_name || item.areaname || item.geography_name,
        metro_area_name: item.metro_area_name,
        smallarea_name: item.smallarea_name,
        county_name: item.countyname,
        state_alpha: item.state_alpha,
        zip_code: item.zip_code,
      }));
      
      return NextResponse.json({
        success: true,
        data: fmrResults,
        source: 'HUD_API'
      });
    }
    
    throw new Error("Unexpected response structure from HUD API");
  } catch (error: any) {
    return NextResponse.json(
      { 
        success: false, 
        statusCode: 500,
        message: error.message || "Internal server error" 
      },
      { status: 500 }
    );
  }
}

/**
 * Fetch HUD metro area data
 */
async function fetchHudMetroData(metroCode: string, year: string): Promise<TopCityResponse | null> {
  try {
    const hudApiKey = process.env.NEXT_HUD_API_KEY;

    if (!hudApiKey) {
      return null;
    }

    const BASE_URL = 'https://www.huduser.gov/hudapi/public/fmr/list';
    const params = new URLSearchParams();
    params.append('year', year);
    params.append('query', metroCode);

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const hudResponse = await fetch(`${BASE_URL}?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${hudApiKey}`,
        'Content-Type': 'application/json'
      },
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!hudResponse.ok) {
      return null;
    }

    const data = await hudResponse.json();
    
    if (data && data.data && Array.isArray(data.data.results) && data.data.results.length > 0) {
      const result = data.data.results[0];
      const avgFmr = (result.fmr0 + result.fmr1 + result.fmr2 + result.fmr3 + result.fmr4) / 5;
      
      return {
        city: result.hud_area_name || result.areaname || 'Unknown',
        state: result.state_alpha || 'Unknown',
        fmrRatio: avgFmr / 1000, // Basic ratio calculation
        vouchers: avgFmr > 1200 ? 'High' : avgFmr > 800 ? 'Medium' : 'Low',
        avgFmr: Math.round(avgFmr),
        data: [result]
      };
    }
    
    return null;
  } catch (error) {
    console.warn(`Error fetching metro data for ${metroCode}:`, error);
    return null;
  }
}

/**
 * Handle market analysis request for a specific state
 */
async function handleMarketAnalysisRequest(state: string, year: string): Promise<NextResponse> {
  try {
    const stateData = await fetchStateWideData(state, year);
    const stateDataJson = await stateData.json();
    
    if (!stateDataJson.success || !stateDataJson.data) {
      return NextResponse.json({
        success: false,
        statusCode: 500,
        message: 'Failed to fetch state data for market analysis'
      }, { status: 500 });
    }

    const fmrData: FmrData[] = stateDataJson.data;
    
    // Calculate market analysis metrics
    const analysis = {
      summary: {
        totalAreas: fmrData.length,
        avgStudio: fmrData.reduce((sum, item) => sum + (item.fmr0 || 0), 0) / fmrData.length,
        avg1BR: fmrData.reduce((sum, item) => sum + (item.fmr1 || 0), 0) / fmrData.length,
        avg2BR: fmrData.reduce((sum, item) => sum + (item.fmr2 || 0), 0) / fmrData.length,
        avg3BR: fmrData.reduce((sum, item) => sum + (item.fmr3 || 0), 0) / fmrData.length,
        avg4BR: fmrData.reduce((sum, item) => sum + (item.fmr4 || 0), 0) / fmrData.length,
      },
      topPerformers: {
        highest2BR: fmrData.sort((a, b) => (b.fmr2 || 0) - (a.fmr2 || 0)).slice(0, 5),
        lowest2BR: fmrData.sort((a, b) => (a.fmr2 || 0) - (b.fmr2 || 0)).slice(0, 5),
      },
      state: state,
      year: year
    };

    return NextResponse.json({
      success: true,
      data: analysis,
      source: 'HUD_API_ANALYSIS'
    });

  } catch (error: any) {
    return NextResponse.json(
      {
        success: false,
        statusCode: 500,
        message: error.message || 'Failed to perform market analysis'
      },
      { status: 500 }
    );
  }
}
