#!/usr/bin/env python3
"""
Development Environment Startup Script
Starts both the Python monitoring backend and Next.js dashboard
"""

import asyncio
import subprocess
import sys
import os
import signal
import time
from pathlib import Path

class DevelopmentEnvironment:
    """Manages the development environment with both backend and frontend"""
    
    def __init__(self):
        self.processes = {}
        self.running = True
        
    def start_backend(self):
        """Start the Python monitoring backend"""
        print("🐍 Starting Python monitoring backend...")
        
        # Start the real-time monitor
        backend_process = subprocess.Popen(
            [sys.executable, "realtime_section8_monitor.py"],
            cwd=Path(__file__).parent,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        self.processes['backend'] = backend_process
        print(f"✅ Backend started (PID: {backend_process.pid})")
        return backend_process
    
    def start_dashboard(self):
        """Start the Next.js dashboard"""
        print("⚛️  Starting Next.js dashboard...")
        
        dashboard_dir = Path(__file__).parent / "dashboard"
        
        # Check if node_modules exists
        if not (dashboard_dir / "node_modules").exists():
            print("📦 Installing dashboard dependencies...")
            install_process = subprocess.run(
                ["npm", "install"],
                cwd=dashboard_dir,
                capture_output=True,
                text=True
            )
            
            if install_process.returncode != 0:
                print(f"❌ Failed to install dependencies: {install_process.stderr}")
                return None
            
            print("✅ Dependencies installed")
        
        # Start the development server
        dashboard_process = subprocess.Popen(
            ["npm", "run", "dev"],
            cwd=dashboard_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        self.processes['dashboard'] = dashboard_process
        print(f"✅ Dashboard started (PID: {dashboard_process.pid})")
        return dashboard_process
    
    def monitor_processes(self):
        """Monitor running processes and restart if needed"""
        while self.running:
            try:
                # Check backend
                if 'backend' in self.processes:
                    backend = self.processes['backend']
                    if backend.poll() is not None:
                        print("⚠️  Backend process died, restarting...")
                        self.start_backend()
                
                # Check dashboard
                if 'dashboard' in self.processes:
                    dashboard = self.processes['dashboard']
                    if dashboard.poll() is not None:
                        print("⚠️  Dashboard process died, restarting...")
                        self.start_dashboard()
                
                time.sleep(5)  # Check every 5 seconds
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ Error monitoring processes: {e}")
                time.sleep(5)
    
    def stop_all(self):
        """Stop all running processes"""
        print("\n🛑 Stopping all processes...")
        self.running = False
        
        for name, process in self.processes.items():
            if process and process.poll() is None:
                print(f"Stopping {name}...")
                try:
                    process.terminate()
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    print(f"Force killing {name}...")
                    process.kill()
                except Exception as e:
                    print(f"Error stopping {name}: {e}")
        
        print("✅ All processes stopped")
    
    def setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            print(f"\n📡 Received signal {signum}")
            self.stop_all()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def check_prerequisites(self):
        """Check if all prerequisites are available"""
        print("🔍 Checking prerequisites...")
        
        # Check Python
        if sys.version_info < (3, 8):
            print("❌ Python 3.8+ required")
            return False
        print("✅ Python version OK")
        
        # Check Node.js
        try:
            result = subprocess.run(["node", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Node.js version: {result.stdout.strip()}")
            else:
                print("❌ Node.js not found")
                return False
        except FileNotFoundError:
            print("❌ Node.js not found")
            return False
        
        # Check npm
        try:
            result = subprocess.run(["npm", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ npm version: {result.stdout.strip()}")
            else:
                print("❌ npm not found")
                return False
        except FileNotFoundError:
            print("❌ npm not found")
            return False
        
        # Check environment variables
        required_env_vars = [
            'REAL_ESTATE_API_KEY',
            'DATABASE_URL',
            'RESEND_API_KEY',
            'ALERT_EMAIL'
        ]
        
        missing_vars = []
        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
            print("💡 Make sure your .env file is properly configured")
            return False
        
        print("✅ Environment variables OK")
        return True
    
    def start(self):
        """Start the development environment"""
        print("🚀 Starting Section 8 Property Monitor Development Environment")
        print("=" * 60)
        
        # Check prerequisites
        if not self.check_prerequisites():
            print("❌ Prerequisites check failed")
            return False
        
        # Set up signal handlers
        self.setup_signal_handlers()
        
        try:
            # Start backend
            backend = self.start_backend()
            if not backend:
                print("❌ Failed to start backend")
                return False
            
            # Wait a moment for backend to initialize
            time.sleep(3)
            
            # Start dashboard
            dashboard = self.start_dashboard()
            if not dashboard:
                print("❌ Failed to start dashboard")
                self.stop_all()
                return False
            
            # Wait for dashboard to start
            time.sleep(5)
            
            print("\n" + "=" * 60)
            print("🎉 Development environment started successfully!")
            print("=" * 60)
            print("📊 Dashboard: http://localhost:3000")
            print("🐍 Backend: Running real-time property monitoring")
            print("📧 Email alerts: <NAME_EMAIL>")
            print("⏱️  Scan interval: 1 minute")
            print("=" * 60)
            print("Press Ctrl+C to stop all services")
            print("=" * 60)
            
            # Monitor processes
            self.monitor_processes()
            
        except KeyboardInterrupt:
            print("\n👋 Shutting down...")
        except Exception as e:
            print(f"❌ Error: {e}")
        finally:
            self.stop_all()
        
        return True

def main():
    """Main entry point"""
    env = DevelopmentEnvironment()
    success = env.start()
    
    if not success:
        print("❌ Failed to start development environment")
        sys.exit(1)

if __name__ == "__main__":
    main()
