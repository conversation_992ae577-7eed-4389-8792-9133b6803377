#!/bin/bash

# Section 8 Property Monitor - Complete System Startup Script
# This script starts all components with system monitoring

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Check if running with sudo for Docker operations
check_sudo() {
    if [[ $EUID -eq 0 ]]; then
        print_status "Running with sudo privileges"
    else
        print_warning "Some operations may require sudo privileges"
    fi
}

# Check prerequisites
check_prerequisites() {
    print_header "CHECKING PREREQUISITES"
    
    # Check Docker
    if command -v docker &> /dev/null; then
        print_status "Docker is installed: $(docker --version)"
    else
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if command -v docker-compose &> /dev/null; then
        print_status "Docker Compose is installed: $(docker-compose --version)"
    else
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check Node.js
    if command -v node &> /dev/null; then
        print_status "Node.js is installed: $(node --version)"
    else
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    # Check Python
    if command -v python3 &> /dev/null; then
        print_status "Python 3 is installed: $(python3 --version)"
    else
        print_error "Python 3 is not installed. Please install Python 3 first."
        exit 1
    fi
    
    # Check if .env file exists
    if [[ -f ".env" ]]; then
        print_status ".env file found"
    else
        print_error ".env file not found. Please create it with required environment variables."
        exit 1
    fi
}

# Create necessary directories
create_directories() {
    print_header "CREATING DIRECTORIES"
    
    directories=("logs" "data" "reports" "grafana/dashboards" "grafana/provisioning" "prometheus" "nginx")
    
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            print_status "Created directory: $dir"
        else
            print_status "Directory exists: $dir"
        fi
    done
}

# Install Python dependencies
install_python_deps() {
    print_header "INSTALLING PYTHON DEPENDENCIES"
    
    if [[ -f "requirements.txt" ]]; then
        print_status "Installing Python dependencies..."
        python3 -m pip install -r requirements.txt
        print_status "Python dependencies installed"
    else
        print_warning "requirements.txt not found, skipping Python dependencies"
    fi
}

# Install Node.js dependencies for dashboards
install_node_deps() {
    print_header "INSTALLING NODE.JS DEPENDENCIES"
    
    # Install Next.js dashboard dependencies
    if [[ -d "dashboard" ]]; then
        print_status "Installing Next.js dashboard dependencies..."
        cd dashboard
        npm install
        cd ..
        print_status "Next.js dashboard dependencies installed"
    fi
    
    # Install Live dashboard dependencies
    if [[ -d "live-dashboard" ]]; then
        print_status "Installing Live dashboard dependencies..."
        cd live-dashboard
        npm install
        cd ..
        print_status "Live dashboard dependencies installed"
    fi
}

# Start Docker Compose stack
start_docker_stack() {
    print_header "STARTING DOCKER COMPOSE STACK"
    
    print_status "Starting PostgreSQL, Redis, Nginx, Prometheus, and Grafana..."
    
    # Use sudo if available, otherwise try without
    if command -v sudo &> /dev/null && [[ $EUID -ne 0 ]]; then
        sudo docker-compose up -d
    else
        docker-compose up -d
    fi
    
    print_status "Docker stack started successfully"
    print_status "Services available at:"
    echo -e "  ${CYAN}• PostgreSQL:${NC} localhost:5432"
    echo -e "  ${CYAN}• Redis:${NC} localhost:6379"
    echo -e "  ${CYAN}• Nginx:${NC} localhost:80"
    echo -e "  ${CYAN}• Prometheus:${NC} localhost:9090"
    echo -e "  ${CYAN}• Grafana:${NC} localhost:3000 (admin/admin123)"
}

# Start Python monitoring systems
start_python_monitors() {
    print_header "STARTING PYTHON MONITORING SYSTEMS"
    
    # Start Production Monitor
    print_status "Starting Production Monitor..."
    nohup python3 production_monitor.py > logs/production_monitor.log 2>&1 &
    PROD_PID=$!
    echo $PROD_PID > logs/production_monitor.pid
    print_status "Production Monitor started (PID: $PROD_PID)"
    
    # Start Main Application
    print_status "Starting Main Application..."
    nohup python3 main.py > logs/main_application.log 2>&1 &
    MAIN_PID=$!
    echo $MAIN_PID > logs/main_application.pid
    print_status "Main Application started (PID: $MAIN_PID)"
}

# Start Node.js dashboards
start_dashboards() {
    print_header "STARTING DASHBOARDS"
    
    # Start Next.js Dashboard
    if [[ -d "dashboard" ]]; then
        print_status "Starting Next.js Dashboard..."
        cd dashboard
        nohup npm run dev > ../logs/nextjs_dashboard.log 2>&1 &
        NEXT_PID=$!
        echo $NEXT_PID > ../logs/nextjs_dashboard.pid
        cd ..
        print_status "Next.js Dashboard started (PID: $NEXT_PID) at http://localhost:3000"
    fi
    
    # Start Live Dashboard
    if [[ -d "live-dashboard" ]]; then
        print_status "Starting Live Dashboard..."
        cd live-dashboard
        nohup npm run dev > ../logs/live_dashboard.log 2>&1 &
        LIVE_PID=$!
        echo $LIVE_PID > ../logs/live_dashboard.pid
        cd ..
        print_status "Live Dashboard started (PID: $LIVE_PID) at http://localhost:3001"
    fi
}

# Display system status
show_system_status() {
    print_header "SYSTEM STATUS"
    
    echo -e "${GREEN}🚀 All systems started successfully!${NC}"
    echo ""
    echo -e "${BLUE}Available Services:${NC}"
    echo -e "  ${CYAN}📊 Next.js Dashboard:${NC} http://localhost:3000"
    echo -e "  ${CYAN}📈 Live Dashboard:${NC} http://localhost:3001"
    echo -e "  ${CYAN}🔍 Network Logs:${NC} http://localhost:3000/network-logs"
    echo -e "  ${CYAN}📉 Grafana Monitoring:${NC} http://localhost:3000 (admin/admin123)"
    echo -e "  ${CYAN}📊 Prometheus Metrics:${NC} http://localhost:9090"
    echo -e "  ${CYAN}🗄️ PostgreSQL:${NC} localhost:5432"
    echo -e "  ${CYAN}⚡ Redis:${NC} localhost:6379"
    echo ""
    echo -e "${BLUE}Log Files:${NC}"
    echo -e "  ${CYAN}• Production Monitor:${NC} logs/production_monitor.log"
    echo -e "  ${CYAN}• Main Application:${NC} logs/main_application.log"
    echo -e "  ${CYAN}• Next.js Dashboard:${NC} logs/nextjs_dashboard.log"
    echo -e "  ${CYAN}• Live Dashboard:${NC} logs/live_dashboard.log"
    echo ""
    echo -e "${BLUE}Process IDs:${NC}"
    if [[ -f "logs/production_monitor.pid" ]]; then
        echo -e "  ${CYAN}• Production Monitor:${NC} $(cat logs/production_monitor.pid)"
    fi
    if [[ -f "logs/main_application.pid" ]]; then
        echo -e "  ${CYAN}• Main Application:${NC} $(cat logs/main_application.pid)"
    fi
    if [[ -f "logs/nextjs_dashboard.pid" ]]; then
        echo -e "  ${CYAN}• Next.js Dashboard:${NC} $(cat logs/nextjs_dashboard.pid)"
    fi
    if [[ -f "logs/live_dashboard.pid" ]]; then
        echo -e "  ${CYAN}• Live Dashboard:${NC} $(cat logs/live_dashboard.pid)"
    fi
}

# Main execution
main() {
    print_header "SECTION 8 PROPERTY MONITOR - SYSTEM STARTUP"
    
    check_sudo
    check_prerequisites
    create_directories
    install_python_deps
    install_node_deps
    start_docker_stack
    
    # Wait a moment for Docker services to start
    print_status "Waiting for Docker services to initialize..."
    sleep 10
    
    start_python_monitors
    start_dashboards
    
    # Wait a moment for all services to start
    sleep 5
    
    show_system_status
    
    echo ""
    echo -e "${GREEN}✅ System startup complete!${NC}"
    echo -e "${YELLOW}💡 Use 'stop_all_systems.sh' to stop all services${NC}"
}

# Run main function
main "$@"
