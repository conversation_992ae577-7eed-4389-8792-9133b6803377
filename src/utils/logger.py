#!/usr/bin/env python3
"""
Logging utilities for Section 8 Investor Pro
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path

def setup_logging(level: str = "INFO", log_dir: str = "logs") -> logging.Logger:
    """
    Setup comprehensive logging for the application
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: Directory to store log files
    
    Returns:
        Configured logger instance
    """
    
    # Create logs directory if it doesn't exist
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    
    # Clear any existing handlers
    root_logger.handlers.clear()
    
    # Set logging level
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    root_logger.setLevel(numeric_level)
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = logging.Formatter(
        fmt='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # Console handler for immediate feedback
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(numeric_level)
    console_handler.setFormatter(simple_formatter)
    root_logger.addHandler(console_handler)
    
    # File handler for detailed logging
    log_file = log_path / f"section8_monitor_{datetime.now().strftime('%Y%m%d')}.log"
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)  # Always log debug to file
    file_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(file_handler)
    
    # Error file handler for errors only
    error_log_file = log_path / f"section8_errors_{datetime.now().strftime('%Y%m%d')}.log"
    error_handler = logging.handlers.RotatingFileHandler(
        error_log_file,
        maxBytes=5 * 1024 * 1024,  # 5MB
        backupCount=3,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(error_handler)
    
    # Performance log handler for monitoring
    perf_log_file = log_path / f"section8_performance_{datetime.now().strftime('%Y%m%d')}.log"
    perf_handler = logging.handlers.RotatingFileHandler(
        perf_log_file,
        maxBytes=5 * 1024 * 1024,  # 5MB
        backupCount=3,
        encoding='utf-8'
    )
    perf_handler.setLevel(logging.INFO)
    
    # Custom formatter for performance logs
    perf_formatter = logging.Formatter(
        fmt='%(asctime)s - PERF - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    perf_handler.setFormatter(perf_formatter)
    
    # Create performance logger
    perf_logger = logging.getLogger('performance')
    perf_logger.addHandler(perf_handler)
    perf_logger.setLevel(logging.INFO)
    perf_logger.propagate = False  # Don't propagate to root logger
    
    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info(f"Logging initialized - Level: {level}, Directory: {log_dir}")
    logger.info(f"Log files: {log_file}, {error_log_file}, {perf_log_file}")
    
    return root_logger

def get_performance_logger() -> logging.Logger:
    """Get the performance logger"""
    return logging.getLogger('performance')

def log_api_call(logger: logging.Logger, api_name: str, endpoint: str, duration: float, success: bool):
    """
    Log API call details for monitoring
    
    Args:
        logger: Logger instance
        api_name: Name of the API (e.g., 'RealEstateAPI', 'HUD_API')
        endpoint: API endpoint called
        duration: Call duration in seconds
        success: Whether the call was successful
    """
    perf_logger = get_performance_logger()
    
    status = "SUCCESS" if success else "FAILED"
    perf_logger.info(f"API_CALL|{api_name}|{endpoint}|{duration:.3f}s|{status}")
    
    if success:
        logger.debug(f"{api_name} call to {endpoint} completed in {duration:.3f}s")
    else:
        logger.warning(f"{api_name} call to {endpoint} failed after {duration:.3f}s")

def log_property_analysis(logger: logging.Logger, address: str, score: int, priority: str, duration: float):
    """
    Log property analysis performance
    
    Args:
        logger: Logger instance
        address: Property address
        score: Investment score
        priority: Investment priority
        duration: Analysis duration in seconds
    """
    perf_logger = get_performance_logger()
    perf_logger.info(f"PROPERTY_ANALYSIS|{address}|{score}|{priority}|{duration:.3f}s")
    
    logger.debug(f"Property analysis completed: {address} - Score: {score}, Priority: {priority} ({duration:.3f}s)")

def log_market_scan(logger: logging.Logger, market: str, properties_found: int, high_priority: int, duration: float):
    """
    Log market scan performance
    
    Args:
        logger: Logger instance
        market: Market name (e.g., "Detroit, MI")
        properties_found: Number of properties found
        high_priority: Number of high priority properties
        duration: Scan duration in seconds
    """
    perf_logger = get_performance_logger()
    perf_logger.info(f"MARKET_SCAN|{market}|{properties_found}|{high_priority}|{duration:.3f}s")
    
    logger.info(f"Market scan completed: {market} - {properties_found} properties, {high_priority} high priority ({duration:.3f}s)")

def log_database_operation(logger: logging.Logger, operation: str, table: str, records: int, duration: float):
    """
    Log database operation performance
    
    Args:
        logger: Logger instance
        operation: Database operation (INSERT, UPDATE, SELECT, etc.)
        table: Table name
        records: Number of records affected
        duration: Operation duration in seconds
    """
    perf_logger = get_performance_logger()
    perf_logger.info(f"DATABASE|{operation}|{table}|{records}|{duration:.3f}s")
    
    logger.debug(f"Database {operation} on {table}: {records} records ({duration:.3f}s)")

def log_email_notification(logger: logging.Logger, alert_type: str, recipient: str, success: bool, duration: float):
    """
    Log email notification performance
    
    Args:
        logger: Logger instance
        alert_type: Type of alert sent
        recipient: Email recipient
        success: Whether email was sent successfully
        duration: Send duration in seconds
    """
    perf_logger = get_performance_logger()
    
    status = "SUCCESS" if success else "FAILED"
    perf_logger.info(f"EMAIL|{alert_type}|{recipient}|{status}|{duration:.3f}s")
    
    if success:
        logger.info(f"Email notification sent: {alert_type} to {recipient} ({duration:.3f}s)")
    else:
        logger.error(f"Email notification failed: {alert_type} to {recipient} ({duration:.3f}s)")

class ContextLogger:
    """Context manager for logging operations with timing"""
    
    def __init__(self, logger: logging.Logger, operation: str, level: int = logging.INFO):
        self.logger = logger
        self.operation = operation
        self.level = level
        self.start_time = None
        
    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.log(self.level, f"Starting {self.operation}...")
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = (datetime.now() - self.start_time).total_seconds()
        
        if exc_type is None:
            self.logger.log(self.level, f"Completed {self.operation} in {duration:.3f}s")
        else:
            self.logger.error(f"Failed {self.operation} after {duration:.3f}s: {exc_val}")
        
        return False  # Don't suppress exceptions

def create_context_logger(logger: logging.Logger, operation: str, level: int = logging.INFO) -> ContextLogger:
    """
    Create a context logger for timing operations
    
    Args:
        logger: Logger instance
        operation: Description of the operation
        level: Logging level
    
    Returns:
        ContextLogger instance
    
    Usage:
        with create_context_logger(logger, "Market scan"):
            # Your operation here
            pass
    """
    return ContextLogger(logger, operation, level)

# Convenience functions for common logging patterns
def log_startup(logger: logging.Logger, component: str, config: dict = None):
    """Log component startup"""
    logger.info(f"=== {component} Starting ===")
    if config:
        for key, value in config.items():
            # Don't log sensitive information
            if any(sensitive in key.lower() for sensitive in ['key', 'password', 'token', 'secret']):
                value = "***REDACTED***"
            logger.info(f"  {key}: {value}")
    logger.info(f"=== {component} Started Successfully ===")

def log_shutdown(logger: logging.Logger, component: str, stats: dict = None):
    """Log component shutdown"""
    logger.info(f"=== {component} Shutting Down ===")
    if stats:
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
    logger.info(f"=== {component} Shutdown Complete ===")

def log_error_with_context(logger: logging.Logger, error: Exception, context: dict = None):
    """Log error with additional context"""
    logger.error(f"Error: {type(error).__name__}: {str(error)}")
    if context:
        logger.error("Error context:")
        for key, value in context.items():
            logger.error(f"  {key}: {value}")

def setup_development_logging():
    """Setup logging for development environment"""
    return setup_logging(level="DEBUG", log_dir="logs")

def setup_production_logging():
    """Setup logging for production environment"""
    return setup_logging(level="INFO", log_dir="/var/log/section8-monitor")

def setup_testing_logging():
    """Setup minimal logging for testing"""
    # Create a simple console-only logger for tests
    logger = logging.getLogger()
    logger.setLevel(logging.WARNING)  # Only show warnings and errors in tests
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Add simple console handler
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(logging.Formatter('%(levelname)s - %(message)s'))
    logger.addHandler(handler)
    
    return logger
