#!/usr/bin/env python3
"""
Utility functions for Section 8 Investor Pro
"""

import asyncio
import hashlib
import json
import os
import time
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
from pathlib import Path

def calculate_hash(data: Union[str, dict, list]) -> str:
    """
    Calculate hash for data deduplication
    
    Args:
        data: Data to hash
        
    Returns:
        SHA256 hash string
    """
    if isinstance(data, (dict, list)):
        data = json.dumps(data, sort_keys=True)
    elif not isinstance(data, str):
        data = str(data)
    
    return hashlib.sha256(data.encode()).hexdigest()

def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for safe file operations
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    # Remove or replace invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    
    # Ensure filename is not empty
    if not filename:
        filename = "unnamed_file"
    
    return filename

def format_currency(amount: Union[int, float]) -> str:
    """
    Format currency amount for display
    
    Args:
        amount: Monetary amount
        
    Returns:
        Formatted currency string
    """
    if amount is None:
        return "$0"
    
    return f"${amount:,.0f}"

def format_percentage(value: Union[int, float], decimal_places: int = 1) -> str:
    """
    Format percentage for display
    
    Args:
        value: Percentage value
        decimal_places: Number of decimal places
        
    Returns:
        Formatted percentage string
    """
    if value is None:
        return "0.0%"
    
    return f"{value:.{decimal_places}f}%"

def calculate_roi(annual_income: float, purchase_price: float) -> float:
    """
    Calculate return on investment
    
    Args:
        annual_income: Annual rental income
        purchase_price: Property purchase price
        
    Returns:
        ROI percentage
    """
    if purchase_price <= 0:
        return 0.0
    
    return (annual_income / purchase_price) * 100

def calculate_cash_flow(monthly_rent: float, monthly_expenses: float) -> float:
    """
    Calculate monthly cash flow
    
    Args:
        monthly_rent: Monthly rental income
        monthly_expenses: Monthly property expenses
        
    Returns:
        Monthly cash flow
    """
    return monthly_rent - monthly_expenses

def estimate_monthly_expenses(purchase_price: float, rent: float = 0) -> Dict[str, float]:
    """
    Estimate monthly property expenses
    
    Args:
        purchase_price: Property purchase price
        rent: Monthly rent (for percentage-based calculations)
        
    Returns:
        Dictionary of expense categories and amounts
    """
    # Conservative expense estimates
    expenses = {
        "property_taxes": purchase_price * 0.015 / 12,  # 1.5% annually
        "insurance": purchase_price * 0.008 / 12,       # 0.8% annually
        "maintenance": purchase_price * 0.01 / 12,      # 1% annually
        "vacancy": rent * 0.05 if rent > 0 else 0,      # 5% of rent
        "property_management": rent * 0.08 if rent > 0 else 0,  # 8% of rent
        "utilities": 50,  # Base utilities estimate
        "other": purchase_price * 0.002 / 12  # 0.2% annually for misc
    }
    
    return expenses

def create_address_key(address: str, city: str = "", state: str = "") -> str:
    """
    Create consistent address key for deduplication
    
    Args:
        address: Street address
        city: City name
        state: State code
        
    Returns:
        Normalized address key
    """
    # Normalize address components
    address_parts = [
        address.upper().strip(),
        city.upper().strip(),
        state.upper().strip()
    ]
    
    # Remove empty parts
    address_parts = [part for part in address_parts if part]
    
    # Create key
    address_key = "_".join(address_parts)
    
    # Remove common variations
    replacements = {
        " STREET": " ST",
        " AVENUE": " AVE", 
        " BOULEVARD": " BLVD",
        " DRIVE": " DR",
        " COURT": " CT",
        " LANE": " LN",
        " ROAD": " RD",
        " PLACE": " PL"
    }
    
    for old, new in replacements.items():
        address_key = address_key.replace(old, new)
    
    return address_key

def validate_property_data(property_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and clean property data
    
    Args:
        property_data: Raw property data
        
    Returns:
        Validated and cleaned property data
    """
    cleaned_data = {}
    
    # Required string fields
    string_fields = ["address", "city", "state", "zip", "propertyType"]
    for field in string_fields:
        value = property_data.get(field, "")
        cleaned_data[field] = str(value).strip() if value else ""
    
    # Integer fields
    int_fields = ["bedrooms", "squareFeet", "lotSquareFeet", "yearBuilt", "estimatedValue"]
    for field in int_fields:
        value = property_data.get(field, 0)
        try:
            cleaned_data[field] = int(value) if value else 0
        except (ValueError, TypeError):
            cleaned_data[field] = 0
    
    # Float fields
    float_fields = ["bathrooms", "latitude", "longitude"]
    for field in float_fields:
        value = property_data.get(field, 0.0)
        try:
            cleaned_data[field] = float(value) if value else 0.0
        except (ValueError, TypeError):
            cleaned_data[field] = 0.0
    
    # Boolean fields
    bool_fields = ["absenteeOwner", "ownerOccupied", "vacant", "foreclosure"]
    for field in bool_fields:
        value = property_data.get(field, False)
        cleaned_data[field] = bool(value)
    
    return cleaned_data

def parse_date_string(date_string: str) -> Optional[datetime]:
    """
    Parse various date string formats
    
    Args:
        date_string: Date string to parse
        
    Returns:
        Parsed datetime object or None
    """
    if not date_string:
        return None
    
    # Common date formats to try
    formats = [
        "%Y-%m-%d",
        "%m/%d/%Y",
        "%m-%d-%Y",
        "%Y/%m/%d",
        "%B %d, %Y",
        "%b %d, %Y",
        "%Y-%m-%d %H:%M:%S",
        "%m/%d/%Y %H:%M:%S"
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(date_string.strip(), fmt)
        except ValueError:
            continue
    
    return None

def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    Split list into chunks of specified size
    
    Args:
        lst: List to chunk
        chunk_size: Size of each chunk
        
    Returns:
        List of chunks
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]

async def retry_async_operation(
    operation, 
    max_retries: int = 3, 
    delay: float = 1.0, 
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,)
):
    """
    Retry async operation with exponential backoff
    
    Args:
        operation: Async function to retry
        max_retries: Maximum number of retries
        delay: Initial delay between retries
        backoff_factor: Backoff multiplier
        exceptions: Exceptions to catch and retry on
        
    Returns:
        Operation result
        
    Raises:
        Last exception if all retries fail
    """
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            if asyncio.iscoroutinefunction(operation):
                return await operation()
            else:
                return operation()
        except exceptions as e:
            last_exception = e
            
            if attempt < max_retries:
                wait_time = delay * (backoff_factor ** attempt)
                await asyncio.sleep(wait_time)
            else:
                raise last_exception

def create_safe_filename(base_name: str, extension: str = "", max_length: int = 255) -> str:
    """
    Create safe filename with length limits
    
    Args:
        base_name: Base filename
        extension: File extension (without dot)
        max_length: Maximum filename length
        
    Returns:
        Safe filename
    """
    # Sanitize base name
    safe_name = sanitize_filename(base_name)
    
    # Add timestamp if name is too generic
    if safe_name.lower() in ["file", "document", "data", "report"]:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_name = f"{safe_name}_{timestamp}"
    
    # Add extension
    if extension:
        if not extension.startswith("."):
            extension = f".{extension}"
        full_name = f"{safe_name}{extension}"
    else:
        full_name = safe_name
    
    # Truncate if too long
    if len(full_name) > max_length:
        # Calculate space for extension
        ext_length = len(extension) if extension else 0
        max_base_length = max_length - ext_length
        
        # Truncate base name
        safe_name = safe_name[:max_base_length]
        full_name = f"{safe_name}{extension}" if extension else safe_name
    
    return full_name

def ensure_directory_exists(directory_path: Union[str, Path]) -> Path:
    """
    Ensure directory exists, create if necessary
    
    Args:
        directory_path: Directory path
        
    Returns:
        Path object for the directory
    """
    path = Path(directory_path)
    path.mkdir(parents=True, exist_ok=True)
    return path

def get_file_size_mb(file_path: Union[str, Path]) -> float:
    """
    Get file size in megabytes
    
    Args:
        file_path: Path to file
        
    Returns:
        File size in MB
    """
    try:
        size_bytes = Path(file_path).stat().st_size
        return size_bytes / (1024 * 1024)
    except OSError:
        return 0.0

def format_duration(seconds: float) -> str:
    """
    Format duration in human-readable format
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"

def calculate_distance_miles(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """
    Calculate distance between two coordinates in miles
    
    Args:
        lat1, lon1: First coordinate pair
        lat2, lon2: Second coordinate pair
        
    Returns:
        Distance in miles
    """
    import math
    
    # Convert to radians
    lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
    
    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    
    # Earth radius in miles
    r = 3956
    
    return c * r

def is_business_hours(dt: datetime = None) -> bool:
    """
    Check if current time is within business hours (9 AM - 6 PM, Mon-Fri)
    
    Args:
        dt: Datetime to check (defaults to now)
        
    Returns:
        True if within business hours
    """
    if dt is None:
        dt = datetime.now()
    
    # Check if weekday (Monday = 0, Sunday = 6)
    if dt.weekday() >= 5:  # Saturday or Sunday
        return False
    
    # Check if within business hours
    return 9 <= dt.hour < 18

def get_market_identifier(city: str, state: str) -> str:
    """
    Create consistent market identifier
    
    Args:
        city: City name
        state: State code
        
    Returns:
        Market identifier string
    """
    return f"{city.upper().strip()}, {state.upper().strip()}"

def extract_zip_code(address: str) -> Optional[str]:
    """
    Extract ZIP code from address string
    
    Args:
        address: Full address string
        
    Returns:
        ZIP code or None
    """
    import re
    
    # Pattern for 5-digit ZIP code
    zip_pattern = r'\b\d{5}\b'
    
    matches = re.findall(zip_pattern, address)
    if matches:
        return matches[-1]  # Return last match (most likely the ZIP)
    
    return None

class RateLimiter:
    """Simple rate limiter for API calls"""
    
    def __init__(self, max_calls: int, time_window: float):
        """
        Initialize rate limiter
        
        Args:
            max_calls: Maximum calls allowed
            time_window: Time window in seconds
        """
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
    
    async def acquire(self):
        """Acquire permission to make a call"""
        now = time.time()
        
        # Remove old calls outside the time window
        self.calls = [call_time for call_time in self.calls if now - call_time < self.time_window]
        
        # Check if we can make a new call
        if len(self.calls) >= self.max_calls:
            # Calculate wait time
            oldest_call = min(self.calls)
            wait_time = self.time_window - (now - oldest_call)
            if wait_time > 0:
                await asyncio.sleep(wait_time)
        
        # Record this call
        self.calls.append(now)

def merge_dictionaries(*dicts: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge multiple dictionaries, with later ones taking precedence
    
    Args:
        *dicts: Dictionaries to merge
        
    Returns:
        Merged dictionary
    """
    result = {}
    
    for d in dicts:
        if d:
            result.update(d)
    
    return result
