#!/usr/bin/env python3
"""
Property Monitor Scheduler for Section 8 Investor Pro
Handles scheduling and coordination of property scanning activities
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import time

from ..services.market_scanner import MarketScanner
from ..services.notification_service import NotificationService
from ..core.database import DatabaseManager
from ..core.config import Config

class PropertyMonitorScheduler:
    """Scheduler for automated property monitoring"""
    
    def __init__(
        self,
        market_scanner: MarketScanner,
        db_manager: DatabaseManager,
        notification_service: NotificationService,
        config: Config
    ):
        """
        Initialize the scheduler
        
        Args:
            market_scanner: Market scanning service
            db_manager: Database manager
            notification_service: Notification service
            config: Application configuration
        """
        self.market_scanner = market_scanner
        self.db_manager = db_manager
        self.notification_service = notification_service
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Scheduler state
        self.is_running = False
        self.current_task = None
        self.scan_count = 0
        self.last_scan_time = None
        self.next_scan_time = None
        
        # Performance tracking
        self.performance_stats = {
            "total_scans": 0,
            "total_properties_found": 0,
            "total_high_priority": 0,
            "avg_scan_time": 0,
            "last_24h_scans": 0,
            "errors_count": 0
        }
        
        # Daily tasks tracking
        self.daily_summary_sent = False
        self.last_daily_summary = None
    
    async def start(self):
        """Start the monitoring scheduler"""
        if self.is_running:
            self.logger.warning("Scheduler is already running")
            return
        
        self.logger.info("Starting Section 8 property monitoring scheduler...")
        self.is_running = True
        
        try:
            # Send startup notification
            await self.notification_service.send_system_alert(
                alert_type="info",
                message="Section 8 property monitoring system started successfully",
                details={
                    "scan_interval": f"{self.config.SEARCH_INTERVAL_MINUTES} minutes",
                    "markets_configured": len(self.config.get_all_markets()),
                    "high_priority_markets": len(self.config.get_high_priority_markets())
                }
            )
            
            # Start the main monitoring loop
            self.current_task = asyncio.create_task(self._monitoring_loop())
            await self.current_task
            
        except asyncio.CancelledError:
            self.logger.info("Scheduler stopped by cancellation")
        except Exception as e:
            self.logger.error(f"Scheduler error: {str(e)}")
            await self.notification_service.send_system_alert(
                alert_type="error",
                message=f"Scheduler encountered an error: {str(e)}",
                details={"error_type": type(e).__name__}
            )
        finally:
            self.is_running = False
    
    async def stop(self):
        """Stop the monitoring scheduler"""
        if not self.is_running:
            self.logger.warning("Scheduler is not running")
            return
        
        self.logger.info("Stopping Section 8 property monitoring scheduler...")
        self.is_running = False
        
        if self.current_task:
            self.current_task.cancel()
            try:
                await self.current_task
            except asyncio.CancelledError:
                pass
        
        # Send shutdown notification
        await self.notification_service.send_system_alert(
            alert_type="info",
            message="Section 8 property monitoring system stopped",
            details=self.get_performance_summary()
        )
        
        self.logger.info("Scheduler stopped successfully")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        self.logger.info(f"Starting monitoring loop with {self.config.SEARCH_INTERVAL_MINUTES} minute intervals")
        
        while self.is_running:
            try:
                # Calculate next scan time
                self.next_scan_time = datetime.now() + timedelta(minutes=self.config.SEARCH_INTERVAL_MINUTES)
                
                # Perform scheduled scan
                await self._perform_scheduled_scan()
                
                # Check for daily tasks
                await self._check_daily_tasks()
                
                # Update performance tracking
                self._update_performance_stats()
                
                # Wait for next interval
                if self.is_running:
                    self.logger.info(f"Next scan scheduled for: {self.next_scan_time.strftime('%H:%M:%S')}")
                    await asyncio.sleep(self.config.SEARCH_INTERVAL_MINUTES * 60)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {str(e)}")
                self.performance_stats["errors_count"] += 1
                
                # Send error notification for critical errors
                await self.notification_service.send_system_alert(
                    alert_type="error",
                    message=f"Monitoring loop error: {str(e)}",
                    details={"scan_count": self.scan_count}
                )
                
                # Wait before retrying (exponential backoff)
                retry_delay = min(300, 60 * (2 ** min(self.performance_stats["errors_count"], 5)))
                self.logger.info(f"Retrying in {retry_delay} seconds...")
                await asyncio.sleep(retry_delay)
    
    async def _perform_scheduled_scan(self):
        """Perform a scheduled property scan"""
        self.logger.info(f"Starting scheduled scan #{self.scan_count + 1}")
        scan_start_time = time.time()
        
        try:
            # Determine scan strategy based on time and performance
            scan_strategy = self._determine_scan_strategy()
            
            # Execute scan based on strategy
            if scan_strategy == "full":
                scan_results = await self.market_scanner.scan_all_markets()
            elif scan_strategy == "priority":
                scan_results = await self.market_scanner.scan_priority_markets("HIGH")
            elif scan_strategy == "quick":
                scan_results = await self.market_scanner.quick_scan(5)
            else:
                # Default to priority scan
                scan_results = await self.market_scanner.scan_priority_markets("HIGH")
            
            # Process scan results
            await self._process_scan_results(scan_results, scan_start_time)
            
            # Update scan statistics
            self.scan_count += 1
            self.last_scan_time = datetime.now()
            
            self.logger.info(f"Scheduled scan completed successfully")
            
        except Exception as e:
            self.logger.error(f"Scheduled scan failed: {str(e)}")
            self.performance_stats["errors_count"] += 1
            
            # Log scan failure to database
            await self._log_scan_failure(str(e))
            
            raise
    
    def _determine_scan_strategy(self) -> str:
        """Determine the best scan strategy based on current conditions"""
        
        current_hour = datetime.now().hour
        
        # Full scan during off-peak hours (2-6 AM)
        if 2 <= current_hour <= 6:
            return "full"
        
        # Priority scan during business hours (9 AM - 6 PM)
        elif 9 <= current_hour <= 18:
            return "priority"
        
        # Quick scan during peak hours to reduce load
        elif 19 <= current_hour <= 23 or 0 <= current_hour <= 1:
            return "quick"
        
        # Default to priority scan
        else:
            return "priority"
    
    async def _process_scan_results(self, scan_results: Dict[str, Any], scan_start_time: float):
        """Process and store scan results"""
        
        try:
            # Extract key metrics
            total_properties = scan_results.get("total_properties", 0)
            high_priority_count = scan_results.get("high_priority_count", 0)
            medium_priority_count = scan_results.get("medium_priority_count", 0)
            scan_time = time.time() - scan_start_time
            
            self.logger.info(
                f"Scan results: {total_properties} properties analyzed, "
                f"{high_priority_count} high priority, {medium_priority_count} medium priority"
            )
            
            # Save properties to database
            await self._save_properties_to_database(scan_results)
            
            # Update market statistics
            await self._update_market_statistics(scan_results)
            
            # Send high priority alerts
            if high_priority_count > 0:
                await self._send_priority_alerts(scan_results)
            
            # Log scan history
            await self._log_scan_history(scan_results, scan_time)
            
            # Update daily statistics
            await self._update_daily_statistics(scan_results)
            
        except Exception as e:
            self.logger.error(f"Error processing scan results: {str(e)}")
            raise
    
    async def _save_properties_to_database(self, scan_results: Dict[str, Any]):
        """Save discovered properties to database"""
        
        saved_count = 0
        error_count = 0
        
        all_properties = scan_results.get("all_properties", [])
        
        for property_result in all_properties:
            try:
                property_data = property_result.get("property_data", {})
                analysis = property_result.get("analysis")
                
                if not analysis:
                    continue
                
                # Prepare property data for database
                db_property_data = {
                    "property_id": str(property_data.get("id", "")),
                    "address": analysis.address,
                    "city": property_data.get("city"),
                    "state": property_data.get("state"),
                    "zip_code": property_data.get("zip"),
                    "county": property_data.get("county"),
                    "bedrooms": property_data.get("bedrooms"),
                    "bathrooms": property_data.get("bathrooms"),
                    "square_feet": property_data.get("squareFeet"),
                    "lot_square_feet": property_data.get("lotSquareFeet"),
                    "year_built": property_data.get("yearBuilt"),
                    "property_type": property_data.get("propertyType"),
                    "property_use": property_data.get("propertyUse"),
                    "estimated_value": property_data.get("estimatedValue"),
                    "assessed_value": property_data.get("assessedValue"),
                    "last_sale_amount": property_data.get("lastSaleAmount"),
                    "estimated_rent": int(analysis.estimated_rent),
                    "fair_market_rent": int(analysis.fair_market_rent),
                    "estimated_cash_flow": int(analysis.estimated_cash_flow),
                    "roi_percentage": analysis.roi_percentage,
                    "investment_score": analysis.investment_score,
                    "investment_priority": analysis.investment_priority,
                    "absentee_owner": property_data.get("absenteeOwner"),
                    "owner_occupied": property_data.get("ownerOccupied"),
                    "vacant": property_data.get("vacant"),
                    "foreclosure": property_data.get("foreclosure"),
                    "latitude": property_data.get("latitude"),
                    "longitude": property_data.get("longitude"),
                    "data_source": "real_estate_api",
                    "raw_data": property_data
                }
                
                # Save to database
                property_id = await self.db_manager.save_property(db_property_data)
                if property_id:
                    saved_count += 1
                
            except Exception as e:
                self.logger.warning(f"Failed to save property: {str(e)}")
                error_count += 1
        
        self.logger.info(f"Saved {saved_count} properties to database ({error_count} errors)")
    
    async def _update_market_statistics(self, scan_results: Dict[str, Any]):
        """Update market statistics in database"""
        
        market_summaries = scan_results.get("market_summaries", [])
        
        for market_summary in market_summaries:
            try:
                market_data = {
                    "city": market_summary.get("city"),
                    "state": market_summary.get("state"),
                    "metro": "",  # Could be added from config
                    "priority": market_summary.get("priority"),
                    "weight": market_summary.get("weight", 1.0),
                    "total_properties_scanned": market_summary.get("total_properties", 0),
                    "high_priority_found": market_summary.get("high_priority", 0),
                    "medium_priority_found": market_summary.get("medium_priority", 0),
                    "low_priority_found": market_summary.get("low_priority", 0),
                    "avg_property_score": 0,  # Could be calculated
                    "avg_roi_percentage": 0,  # Could be calculated
                    "avg_property_price": 0,  # Could be calculated
                    "fmr_data": {},
                    "fmr_last_updated": datetime.now()
                }
                
                await self.db_manager.update_market_stats(market_data)
                
            except Exception as e:
                self.logger.warning(f"Failed to update market stats: {str(e)}")
    
    async def _send_priority_alerts(self, scan_results: Dict[str, Any]):
        """Send alerts for high priority properties"""
        
        high_priority_properties = scan_results.get("high_priority_properties", [])
        
        if high_priority_properties:
            try:
                # Send high priority alert
                success = await self.notification_service.send_high_priority_alert(
                    high_priority_properties
                )
                
                if success:
                    self.logger.info(f"High priority alert sent for {len(high_priority_properties)} properties")
                    
                    # Log alert to database
                    for prop in high_priority_properties:
                        alert_data = {
                            "alert_type": "high_priority_property",
                            "property_id": None,  # Would need property DB ID
                            "subject": f"High Priority Section 8 Opportunity",
                            "message": f"New high priority property: {prop.get('analysis', {}).get('address', 'Unknown')}",
                            "priority": "HIGH",
                            "recipient_email": self.config.ALERT_EMAIL,
                            "alert_data": {
                                "investment_score": prop.get("analysis", {}).get("investment_score", 0),
                                "roi_percentage": prop.get("analysis", {}).get("roi_percentage", 0)
                            }
                        }
                        
                        try:
                            await self.db_manager.save_alert(alert_data)
                        except Exception as e:
                            self.logger.warning(f"Failed to save alert to database: {str(e)}")
                
            except Exception as e:
                self.logger.error(f"Failed to send priority alerts: {str(e)}")
    
    async def _log_scan_history(self, scan_results: Dict[str, Any], scan_time: float):
        """Log scan history to database"""
        
        try:
            # Create aggregated scan history entry
            scan_data = {
                "market_city": "ALL_MARKETS",
                "market_state": "ALL",
                "search_criteria": {
                    "markets_scanned": scan_results.get("markets_scanned", 0),
                    "scan_strategy": "scheduled_scan"
                },
                "properties_found": scan_results.get("total_properties", 0),
                "properties_analyzed": scan_results.get("total_properties", 0),
                "high_priority_found": scan_results.get("high_priority_count", 0),
                "medium_priority_found": scan_results.get("medium_priority_count", 0),
                "execution_time_seconds": scan_time,
                "api_calls_made": scan_results.get("total_api_calls", 0),
                "search_status": "completed",
                "error_message": None
            }
            
            await self.db_manager.log_search_history(scan_data)
            
        except Exception as e:
            self.logger.warning(f"Failed to log scan history: {str(e)}")
    
    async def _log_scan_failure(self, error_message: str):
        """Log scan failure to database"""
        
        try:
            scan_data = {
                "market_city": "ALL_MARKETS",
                "market_state": "ALL",
                "search_criteria": {"scan_strategy": "scheduled_scan"},
                "properties_found": 0,
                "properties_analyzed": 0,
                "high_priority_found": 0,
                "medium_priority_found": 0,
                "execution_time_seconds": 0,
                "api_calls_made": 0,
                "search_status": "failed",
                "error_message": error_message
            }
            
            await self.db_manager.log_search_history(scan_data)
            
        except Exception as e:
            self.logger.warning(f"Failed to log scan failure: {str(e)}")
    
    async def _update_daily_statistics(self, scan_results: Dict[str, Any]):
        """Update daily statistics"""
        
        try:
            stats_data = {
                "total_searches": 1,
                "total_api_calls": scan_results.get("total_api_calls", 0),
                "total_properties_analyzed": scan_results.get("total_properties", 0),
                "new_properties_found": len(scan_results.get("all_properties", [])),
                "high_priority_properties": scan_results.get("high_priority_count", 0),
                "medium_priority_properties": scan_results.get("medium_priority_count", 0),
                "avg_search_time": scan_results.get("total_scan_time", 0),
                "avg_property_score": 0,  # Could be calculated
                "avg_roi_percentage": 0,  # Could be calculated
                "alerts_sent": 1 if scan_results.get("high_priority_count", 0) > 0 else 0
            }
            
            await self.db_manager.update_daily_stats(stats_data)
            
        except Exception as e:
            self.logger.warning(f"Failed to update daily statistics: {str(e)}")
    
    async def _check_daily_tasks(self):
        """Check and perform daily tasks"""
        
        current_time = datetime.now()
        current_date = current_time.date()
        
        # Check if we need to send daily summary (once per day at 6 PM)
        if (current_time.hour == 18 and 
            not self.daily_summary_sent and 
            (not self.last_daily_summary or self.last_daily_summary.date() < current_date)):
            
            await self._send_daily_summary()
    
    async def _send_daily_summary(self):
        """Send daily summary email"""
        
        try:
            # Get dashboard stats for summary
            dashboard_stats = await self.db_manager.get_dashboard_stats()
            
            # Create summary data
            summary_data = {
                "date": datetime.now().strftime("%Y-%m-%d"),
                "total_properties": dashboard_stats.get("total_properties", 0),
                "high_priority_count": dashboard_stats.get("high_priority", 0),
                "medium_priority_count": dashboard_stats.get("medium_priority", 0),
                "avg_roi": dashboard_stats.get("avg_roi", 0),
                "today_found": dashboard_stats.get("today_found", 0),
                "scan_count": self.scan_count,
                "market_summaries": []  # Could be populated with recent data
            }
            
            # Send daily summary
            success = await self.notification_service.send_daily_summary(summary_data)
            
            if success:
                self.daily_summary_sent = True
                self.last_daily_summary = datetime.now()
                self.logger.info("Daily summary email sent successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to send daily summary: {str(e)}")
    
    def _update_performance_stats(self):
        """Update performance statistics"""
        
        self.performance_stats["total_scans"] = self.scan_count
        
        # Calculate 24-hour scan count
        if self.last_scan_time:
            twenty_four_hours_ago = datetime.now() - timedelta(hours=24)
            if self.last_scan_time >= twenty_four_hours_ago:
                self.performance_stats["last_24h_scans"] += 1
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        
        uptime = None
        if self.last_scan_time:
            uptime = (datetime.now() - self.last_scan_time).total_seconds()
        
        return {
            "is_running": self.is_running,
            "total_scans": self.scan_count,
            "last_scan_time": self.last_scan_time.isoformat() if self.last_scan_time else None,
            "next_scan_time": self.next_scan_time.isoformat() if self.next_scan_time else None,
            "scan_interval_minutes": self.config.SEARCH_INTERVAL_MINUTES,
            "performance_stats": self.performance_stats,
            "uptime_seconds": uptime
        }
    
    async def trigger_immediate_scan(self, scan_type: str = "priority") -> Dict[str, Any]:
        """Trigger an immediate scan outside of the regular schedule"""
        
        self.logger.info(f"Triggering immediate {scan_type} scan...")
        
        try:
            if scan_type == "full":
                results = await self.market_scanner.scan_all_markets()
            elif scan_type == "priority":
                results = await self.market_scanner.scan_priority_markets("HIGH")
            elif scan_type == "quick":
                results = await self.market_scanner.quick_scan(3)
            else:
                raise ValueError(f"Unknown scan type: {scan_type}")
            
            # Process results
            scan_start_time = time.time()
            await self._process_scan_results(results, scan_start_time)
            
            self.logger.info(f"Immediate {scan_type} scan completed successfully")
            return results
            
        except Exception as e:
            self.logger.error(f"Immediate scan failed: {str(e)}")
            raise
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        
        # Get scanner stats
        scanner_stats = self.market_scanner.get_scan_statistics()
        
        # Get notification stats
        notification_stats = self.notification_service.get_notification_stats()
        
        # Get database stats
        try:
            db_stats = await self.db_manager.get_dashboard_stats()
        except Exception as e:
            self.logger.warning(f"Could not get database stats: {str(e)}")
            db_stats = {}
        
        return {
            "scheduler": self.get_performance_summary(),
            "scanner": scanner_stats,
            "notifications": notification_stats,
            "database": db_stats,
            "configuration": self.config.to_dict(),
            "system_time": datetime.now().isoformat()
        }
