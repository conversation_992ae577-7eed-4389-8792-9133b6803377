<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Section 8 Investor Pro - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-home"></i> Section 8 Investor Pro
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#properties">Properties</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#markets">Markets</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#settings">Settings</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text" id="system-status">
                            <i class="fas fa-circle text-success"></i> Online
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Alert Messages -->
        <div id="alerts-container"></div>

        <!-- Main Dashboard -->
        <div class="row">
            <!-- Statistics Cards -->
            <div class="col-12">
                <div class="row mb-4">
                    <div class="col-md-3 col-sm-6 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="total-properties">{{ stats.total_properties or 0 }}</h4>
                                        <p class="card-text">Total Properties</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-building fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 col-sm-6 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="high-priority">{{ stats.high_priority or 0 }}</h4>
                                        <p class="card-text">High Priority</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-star fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 col-sm-6 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="avg-roi">{{ "%.1f"|format(stats.avg_roi or 0) }}%</h4>
                                        <p class="card-text">Average ROI</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-chart-line fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 col-sm-6 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="today-found">{{ stats.today_found or 0 }}</h4>
                                        <p class="card-text">Found Today</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-calendar-day fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Control Panel -->
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> Control Panel</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="triggerScan('priority')">
                                <i class="fas fa-search"></i> Quick Scan
                            </button>
                            <button class="btn btn-warning" onclick="triggerScan('full')">
                                <i class="fas fa-globe"></i> Full Market Scan
                            </button>
                            <button class="btn btn-info" onclick="sendTestEmail()">
                                <i class="fas fa-envelope"></i> Test Email
                            </button>
                            <button class="btn btn-success" id="monitoring-toggle" onclick="toggleMonitoring()">
                                <i class="fas fa-play"></i> Start Monitoring
                            </button>
                        </div>
                        
                        <hr>
                        
                        <div class="mb-3">
                            <label class="form-label">System Status</label>
                            <div id="system-status-details">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                                Loading...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Properties -->
            <div class="col-md-8 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list"></i> Recent High Priority Properties</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshProperties()">
                            <i class="fas fa-refresh"></i> Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="properties-loading" class="text-center">
                            <div class="spinner-border" role="status"></div>
                            <p>Loading properties...</p>
                        </div>
                        <div id="properties-table" style="display: none;">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Address</th>
                                            <th>Price</th>
                                            <th>Score</th>
                                            <th>ROI</th>
                                            <th>Cash Flow</th>
                                            <th>Priority</th>
                                        </tr>
                                    </thead>
                                    <tbody id="properties-tbody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Markets Overview -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-map"></i> Configured Markets</h5>
                    </div>
                    <div class="card-body">
                        <div id="markets-loading" class="text-center">
                            <div class="spinner-border" role="status"></div>
                            <p>Loading markets...</p>
                        </div>
                        <div id="markets-grid" class="row" style="display: none;">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logs Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-file-alt"></i> Recent Logs</h5>
                        <div>
                            <select class="form-select form-select-sm" id="log-level" onchange="refreshLogs()">
                                <option value="INFO">INFO</option>
                                <option value="WARNING">WARNING</option>
                                <option value="ERROR">ERROR</option>
                                <option value="DEBUG">DEBUG</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="logs-container" style="max-height: 300px; overflow-y: auto;">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                                Loading logs...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Dashboard JavaScript -->
    <script src="/static/js/dashboard.js"></script>
    
    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
        });
    </script>
</body>
</html>
