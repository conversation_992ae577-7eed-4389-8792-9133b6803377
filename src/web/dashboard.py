#!/usr/bin/env python3
"""
Section 8 Investor Pro - Web Dashboard API
FastAPI-based web interface for monitoring and managing the system
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json

from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi import Request
from pydantic import BaseModel
import uvicorn

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.core.config import Config
from src.core.database import DatabaseManager
from src.api.real_estate_client import RealEstateAPIClient
from src.api.hud_client import HUDAPIClient
from src.services.market_scanner import MarketScanner
from src.services.property_analyzer import PropertyAnalyzer
from src.services.notification_service import NotificationService
from src.core.scheduler import PropertyMonitorScheduler
from src.utils.logger import setup_logging

# Configuration
config = Config()
logger = setup_logging(config.LOG_LEVEL)

# FastAPI app
app = FastAPI(
    title="Section 8 Investor Pro",
    description="Advanced Property Monitoring Dashboard",
    version="2.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Templates
templates = Jinja2Templates(directory="src/web/templates")

# Static files
app.mount("/static", StaticFiles(directory="src/web/static"), name="static")

# Global services (will be initialized on startup)
db_manager: Optional[DatabaseManager] = None
market_scanner: Optional[MarketScanner] = None
notification_service: Optional[NotificationService] = None
scheduler: Optional[PropertyMonitorScheduler] = None

# Pydantic models for API
class PropertySummary(BaseModel):
    id: int
    address: str
    city: str
    state: str
    estimated_value: int
    investment_score: int
    investment_priority: str
    roi_percentage: float
    estimated_rent: int
    estimated_cash_flow: int
    bedrooms: int
    bathrooms: float
    first_discovered: datetime

class MarketSummary(BaseModel):
    city: str
    state: str
    priority: str
    total_properties_scanned: int
    high_priority_found: int
    medium_priority_found: int
    last_scanned: Optional[datetime]

class SystemStats(BaseModel):
    total_properties: int
    high_priority: int
    medium_priority: int
    avg_roi: float
    today_found: int
    markets_configured: int
    scan_interval_minutes: int

class ScanRequest(BaseModel):
    scan_type: str = "priority"  # priority, full, quick
    markets: Optional[List[str]] = None
    criteria_type: str = "primary"

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    global db_manager, market_scanner, notification_service, scheduler
    
    try:
        logger.info("Starting Section 8 Investor Pro Web Dashboard...")
        
        # Initialize database
        db_manager = DatabaseManager(config.DATABASE_URL)
        await db_manager.initialize()
        
        # Initialize API clients
        re_client = RealEstateAPIClient(config.REAL_ESTATE_API_KEY, config.REAL_ESTATE_API_URL)
        hud_client = HUDAPIClient(config.HUD_API_KEY, config.HUD_API_URL)
        
        # Initialize services
        property_analyzer = PropertyAnalyzer(re_client, hud_client, config)
        market_scanner = MarketScanner(re_client, property_analyzer, config)
        notification_service = NotificationService(config)
        
        # Initialize scheduler (but don't start automatic scheduling)
        scheduler = PropertyMonitorScheduler(
            market_scanner=market_scanner,
            db_manager=db_manager,
            notification_service=notification_service,
            config=config
        )
        
        logger.info("Web dashboard initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize web dashboard: {str(e)}")
        raise

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global db_manager, scheduler
    
    try:
        if scheduler and scheduler.is_running:
            await scheduler.stop()
        
        if db_manager:
            await db_manager.close()
        
        logger.info("Web dashboard shutdown complete")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {str(e)}")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

# Main dashboard page
@app.get("/", response_class=HTMLResponse)
async def dashboard_page(request: Request):
    """Main dashboard page"""
    try:
        # Get basic stats for the dashboard
        stats = await db_manager.get_dashboard_stats()
        
        return templates.TemplateResponse("dashboard.html", {
            "request": request,
            "stats": stats,
            "config": config.to_dict()
        })
    except Exception as e:
        logger.error(f"Dashboard page error: {str(e)}")
        raise HTTPException(status_code=500, detail="Dashboard unavailable")

# API Routes

@app.get("/api/stats", response_model=SystemStats)
async def get_system_stats():
    """Get system statistics"""
    try:
        stats = await db_manager.get_dashboard_stats()
        
        return SystemStats(
            total_properties=stats.get('total_properties', 0),
            high_priority=stats.get('high_priority', 0),
            medium_priority=stats.get('medium_priority', 0),
            avg_roi=stats.get('avg_roi', 0.0),
            today_found=stats.get('today_found', 0),
            markets_configured=len(config.get_all_markets()),
            scan_interval_minutes=config.SEARCH_INTERVAL_MINUTES
        )
    except Exception as e:
        logger.error(f"Stats API error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get statistics")

@app.get("/api/properties", response_model=List[PropertySummary])
async def get_properties(
    priority: Optional[str] = Query(None, description="Filter by priority (HIGH, MEDIUM, LOW)"),
    limit: int = Query(50, description="Maximum number of properties to return"),
    days_back: int = Query(7, description="Number of days to look back")
):
    """Get list of properties"""
    try:
        if priority:
            # Filter by priority in the database query (would need to implement)
            properties = await db_manager.get_high_priority_properties(limit=limit, days_back=days_back)
        else:
            properties = await db_manager.get_high_priority_properties(limit=limit, days_back=days_back)
        
        return [
            PropertySummary(
                id=prop.get('id', 0),
                address=prop.get('address', ''),
                city=prop.get('city', ''),
                state=prop.get('state', ''),
                estimated_value=prop.get('estimated_value', 0),
                investment_score=prop.get('investment_score', 0),
                investment_priority=prop.get('investment_priority', ''),
                roi_percentage=prop.get('roi_percentage', 0.0),
                estimated_rent=prop.get('estimated_rent', 0),
                estimated_cash_flow=prop.get('estimated_cash_flow', 0),
                bedrooms=prop.get('bedrooms', 0),
                bathrooms=prop.get('bathrooms', 0.0),
                first_discovered=prop.get('first_discovered', datetime.now())
            )
            for prop in properties
        ]
        
    except Exception as e:
        logger.error(f"Properties API error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get properties")

@app.get("/api/markets")
async def get_markets():
    """Get configured markets"""
    try:
        markets = config.get_all_markets()
        
        return [
            {
                "city": market.city,
                "state": market.state,
                "metro": market.metro,
                "priority": market.priority,
                "weight": market.weight
            }
            for market in markets
        ]
        
    except Exception as e:
        logger.error(f"Markets API error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get markets")

@app.post("/api/scan")
async def trigger_scan(scan_request: ScanRequest, background_tasks: BackgroundTasks):
    """Trigger a property scan"""
    try:
        if not scheduler:
            raise HTTPException(status_code=503, detail="Scanner not available")
        
        # Add scan to background tasks
        async def run_scan():
            try:
                if scan_request.scan_type == "full":
                    await market_scanner.scan_all_markets()
                elif scan_request.scan_type == "quick":
                    await market_scanner.quick_scan(5)
                else:  # priority
                    await market_scanner.scan_priority_markets("HIGH")
                
                logger.info(f"Manual {scan_request.scan_type} scan completed")
                
            except Exception as e:
                logger.error(f"Manual scan failed: {str(e)}")
        
        background_tasks.add_task(run_scan)
        
        return {"message": f"{scan_request.scan_type} scan started", "status": "initiated"}
        
    except Exception as e:
        logger.error(f"Scan trigger error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to trigger scan")

@app.get("/api/system-status")
async def get_system_status():
    """Get comprehensive system status"""
    try:
        # Test API connections
        api_status = {}
        
        # Test Real Estate API
        async with RealEstateAPIClient(config.REAL_ESTATE_API_KEY, config.REAL_ESTATE_API_URL) as re_client:
            api_status['real_estate_api'] = await re_client.test_connection()
        
        # Test HUD API
        async with HUDAPIClient(config.HUD_API_KEY, config.HUD_API_URL) as hud_client:
            api_status['hud_api'] = await hud_client.test_connection()
        
        # Database status
        try:
            await db_manager.get_dashboard_stats()
            api_status['database'] = True
        except:
            api_status['database'] = False
        
        # Email service status
        email_stats = notification_service.get_notification_stats()
        api_status['email_service'] = email_stats.get('service_status') == 'active'
        
        # Scheduler status
        scheduler_status = None
        if scheduler:
            scheduler_status = {
                "is_running": scheduler.is_running,
                "scan_count": scheduler.scan_count,
                "last_scan_time": scheduler.last_scan_time.isoformat() if scheduler.last_scan_time else None
            }
        
        return {
            "timestamp": datetime.now().isoformat(),
            "api_status": api_status,
            "scheduler_status": scheduler_status,
            "configuration": config.to_dict(),
            "notification_stats": email_stats
        }
        
    except Exception as e:
        logger.error(f"System status error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get system status")

@app.post("/api/start-monitoring")
async def start_monitoring():
    """Start automatic monitoring"""
    try:
        if not scheduler:
            raise HTTPException(status_code=503, detail="Scheduler not available")
        
        if scheduler.is_running:
            return {"message": "Monitoring is already running", "status": "running"}
        
        # Start monitoring in background
        asyncio.create_task(scheduler.start())
        
        return {"message": "Monitoring started", "status": "started"}
        
    except Exception as e:
        logger.error(f"Start monitoring error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to start monitoring")

@app.post("/api/stop-monitoring")
async def stop_monitoring():
    """Stop automatic monitoring"""
    try:
        if not scheduler:
            raise HTTPException(status_code=503, detail="Scheduler not available")
        
        if not scheduler.is_running:
            return {"message": "Monitoring is not running", "status": "stopped"}
        
        await scheduler.stop()
        
        return {"message": "Monitoring stopped", "status": "stopped"}
        
    except Exception as e:
        logger.error(f"Stop monitoring error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to stop monitoring")

@app.post("/api/send-test-email")
async def send_test_email():
    """Send test email"""
    try:
        success = await notification_service.send_system_alert(
            alert_type="info",
            message="Test email from Section 8 Investor Pro Dashboard",
            details={"sent_from": "web_dashboard", "timestamp": datetime.now().isoformat()}
        )
        
        if success:
            return {"message": "Test email sent successfully", "status": "sent"}
        else:
            return {"message": "Test email failed", "status": "failed"}
            
    except Exception as e:
        logger.error(f"Test email error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to send test email")

@app.get("/api/logs")
async def get_logs(
    lines: int = Query(100, description="Number of log lines to return"),
    level: str = Query("INFO", description="Log level filter")
):
    """Get recent log entries"""
    try:
        log_file = Path("logs") / f"section8_monitor_{datetime.now().strftime('%Y%m%d')}.log"
        
        if not log_file.exists():
            return {"logs": [], "message": "Log file not found"}
        
        with open(log_file, 'r') as f:
            log_lines = f.readlines()
        
        # Filter by level and get recent entries
        filtered_lines = [
            line.strip() for line in log_lines[-lines:]
            if level.upper() in line
        ]
        
        return {"logs": filtered_lines, "total_lines": len(filtered_lines)}
        
    except Exception as e:
        logger.error(f"Logs API error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get logs")

@app.get("/api/export/properties")
async def export_properties(
    format: str = Query("json", description="Export format (json, csv)"),
    priority: Optional[str] = Query(None, description="Filter by priority")
):
    """Export properties data"""
    try:
        properties = await db_manager.get_high_priority_properties(limit=1000)
        
        if format.lower() == "csv":
            import pandas as pd
            
            df = pd.DataFrame(properties)
            csv_content = df.to_csv(index=False)
            
            return Response(
                content=csv_content,
                media_type="text/csv",
                headers={"Content-Disposition": "attachment; filename=properties.csv"}
            )
        else:
            # JSON format
            return {"properties": properties, "count": len(properties)}
            
    except Exception as e:
        logger.error(f"Export error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to export properties")

# Run the web server
def run_web_server(host: str = "0.0.0.0", port: int = 8000, reload: bool = False):
    """Run the web server"""
    uvicorn.run(
        "src.web.dashboard:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )

if __name__ == "__main__":
    run_web_server(reload=True)
