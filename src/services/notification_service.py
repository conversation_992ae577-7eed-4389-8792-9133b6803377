#!/usr/bin/env python3
"""
Notification Service for Section 8 Investor Pro
Handles email alerts and notifications for investment opportunities
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import json

from ..core.config import Config
from ..services.property_analyzer import PropertyAnalysis

class NotificationService:
    """Handles notifications and alerts for investment opportunities"""
    
    def __init__(self, config: Config):
        """
        Initialize notification service
        
        Args:
            config: Application configuration
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.session = None
        
        # Email configuration
        self.resend_api_key = config.RESEND_API_KEY
        self.alert_email = config.ALERT_EMAIL
        self.resend_url = "https://api.resend.com/emails"
        
        # Notification tracking
        self.sent_count = 0
        self.failed_count = 0
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self._get_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def send_high_priority_alert(
        self, 
        properties: List[Dict[str, Any]]
    ) -> bool:
        """
        Send email alert for high priority properties
        
        Args:
            properties: List of high priority property results
        
        Returns:
            True if email sent successfully, False otherwise
        """
        if not properties:
            return True  # No properties to alert about
        
        try:
            # Filter for truly high priority properties
            high_priority_props = [
                prop for prop in properties 
                if prop.get("analysis") and prop["analysis"].investment_priority == "HIGH"
            ]
            
            if not high_priority_props:
                return True  # No high priority properties
            
            # Create email content
            subject = f"🏠 {len(high_priority_props)} High-Priority Section 8 Opportunities Found!"
            html_content = self._create_high_priority_email(high_priority_props)
            
            # Send email
            success = await self._send_email(
                to_email=self.alert_email,
                subject=subject,
                html_content=html_content,
                alert_type="high_priority"
            )
            
            if success:
                self.logger.info(f"High priority alert sent: {len(high_priority_props)} properties")
                self.sent_count += 1
            else:
                self.logger.error("Failed to send high priority alert")
                self.failed_count += 1
            
            return success
            
        except Exception as e:
            self.logger.error(f"High priority alert failed: {str(e)}")
            self.failed_count += 1
            return False
    
    async def send_daily_summary(
        self, 
        scan_results: Dict[str, Any]
    ) -> bool:
        """
        Send daily summary email with scan results
        
        Args:
            scan_results: Compiled scan results from market scanner
        
        Returns:
            True if email sent successfully
        """
        try:
            subject = f"📊 Section 8 Daily Summary - {scan_results.get('high_priority_count', 0)} Opportunities"
            html_content = self._create_daily_summary_email(scan_results)
            
            success = await self._send_email(
                to_email=self.alert_email,
                subject=subject,
                html_content=html_content,
                alert_type="daily_summary"
            )
            
            if success:
                self.logger.info("Daily summary email sent successfully")
                self.sent_count += 1
            else:
                self.logger.error("Failed to send daily summary email")
                self.failed_count += 1
            
            return success
            
        except Exception as e:
            self.logger.error(f"Daily summary email failed: {str(e)}")
            self.failed_count += 1
            return False
    
    async def send_system_alert(
        self, 
        alert_type: str, 
        message: str, 
        details: Dict[str, Any] = None
    ) -> bool:
        """
        Send system alert for errors or important events
        
        Args:
            alert_type: Type of alert (error, warning, info)
            message: Alert message
            details: Additional details
        
        Returns:
            True if email sent successfully
        """
        try:
            emoji_map = {
                "error": "🚨",
                "warning": "⚠️",
                "info": "ℹ️",
                "success": "✅"
            }
            
            emoji = emoji_map.get(alert_type, "📢")
            subject = f"{emoji} Section 8 Monitor System Alert - {alert_type.title()}"
            
            html_content = self._create_system_alert_email(alert_type, message, details)
            
            success = await self._send_email(
                to_email=self.alert_email,
                subject=subject,
                html_content=html_content,
                alert_type="system_alert"
            )
            
            if success:
                self.logger.info(f"System alert sent: {alert_type}")
                self.sent_count += 1
            else:
                self.logger.error(f"Failed to send system alert: {alert_type}")
                self.failed_count += 1
            
            return success
            
        except Exception as e:
            self.logger.error(f"System alert failed: {str(e)}")
            self.failed_count += 1
            return False
    
    async def send_market_update(
        self, 
        market_summaries: List[Dict[str, Any]]
    ) -> bool:
        """
        Send market performance update
        
        Args:
            market_summaries: List of market summary data
        
        Returns:
            True if email sent successfully
        """
        try:
            total_opportunities = sum(m.get('opportunities_found', 0) for m in market_summaries)
            subject = f"📈 Market Update - {total_opportunities} New Opportunities Across {len(market_summaries)} Markets"
            
            html_content = self._create_market_update_email(market_summaries)
            
            success = await self._send_email(
                to_email=self.alert_email,
                subject=subject,
                html_content=html_content,
                alert_type="market_update"
            )
            
            if success:
                self.logger.info("Market update email sent successfully")
                self.sent_count += 1
            else:
                self.logger.error("Failed to send market update email")
                self.failed_count += 1
            
            return success
            
        except Exception as e:
            self.logger.error(f"Market update email failed: {str(e)}")
            self.failed_count += 1
            return False
    
    async def _send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        alert_type: str
    ) -> bool:
        """
        Send email using Resend API
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            html_content: HTML email content
            alert_type: Type of alert for tracking
        
        Returns:
            True if email sent successfully
        """
        try:
            session = await self._get_session()
            
            # Prepare email payload
            payload = {
                "from": f"Section8 Monitor <monitor@{self._get_domain()}>",
                "to": [to_email],
                "subject": subject,
                "html": html_content
            }
            
            # Set headers
            headers = {
                "Authorization": f"Bearer {self.resend_api_key}",
                "Content-Type": "application/json"
            }
            
            # Send email
            async with session.post(self.resend_url, headers=headers, json=payload) as response:
                if response.status == 200:
                    response_data = await response.json()
                    self.logger.debug(f"Email sent successfully: {response_data.get('id')}")
                    return True
                else:
                    error_text = await response.text()
                    self.logger.error(f"Email send failed: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Email send exception: {str(e)}")
            return False
    
    def _get_domain(self) -> str:
        """Get domain for email from address"""
        # Extract domain from app URL or use default
        if self.config.APP_URL:
            try:
                from urllib.parse import urlparse
                parsed = urlparse(self.config.APP_URL)
                if parsed.netloc:
                    return parsed.netloc
            except:
                pass
        
        return "section8investor.com"  # Default domain
    
    def _create_high_priority_email(self, properties: List[Dict[str, Any]]) -> str:
        """Create HTML content for high priority property alert"""
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <title>High Priority Section 8 Opportunities</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 800px; margin: 0 auto; padding: 20px; }}
                .header {{ background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                .property {{ border: 1px solid #ddd; margin: 15px 0; padding: 20px; border-radius: 8px; background: #f9f9f9; }}
                .property-header {{ color: #2c3e50; font-size: 1.2em; font-weight: bold; margin-bottom: 10px; }}
                .highlight {{ background: #e8f5e8; padding: 2px 6px; border-radius: 4px; }}
                .metrics {{ display: flex; gap: 20px; margin: 10px 0; }}
                .metric {{ background: white; padding: 10px; border-radius: 5px; text-align: center; }}
                .reasons {{ margin: 10px 0; }}
                .reason {{ margin: 5px 0; padding: 5px; background: white; border-radius: 4px; }}
                .footer {{ margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🏠 High Priority Section 8 Investment Opportunities</h1>
                    <p>Found {len(properties)} exceptional properties matching your investment criteria</p>
                    <p><small>Alert generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</small></p>
                </div>
        """
        
        # Add each property
        for i, prop in enumerate(properties[:10], 1):  # Limit to top 10
            analysis = prop.get("analysis")
            property_data = prop.get("property_data", {})
            
            if not analysis:
                continue
            
            html += f"""
                <div class="property">
                    <div class="property-header">
                        {i}. {analysis.address}
                    </div>
                    
                    <div class="metrics">
                        <div class="metric">
                            <strong>${property_data.get('estimatedValue', 0):,}</strong><br>
                            <small>Purchase Price</small>
                        </div>
                        <div class="metric">
                            <strong>{analysis.investment_score}/100</strong><br>
                            <small>Investment Score</small>
                        </div>
                        <div class="metric">
                            <strong>{analysis.roi_percentage:.1f}%</strong><br>
                            <small>ROI</small>
                        </div>
                        <div class="metric">
                            <strong>${analysis.estimated_cash_flow:.0f}</strong><br>
                            <small>Monthly Cash Flow</small>
                        </div>
                    </div>
                    
                    <p><strong>Property Details:</strong> 
                    {property_data.get('bedrooms', 0)}BR/{property_data.get('bathrooms', 0)}BA | 
                    {property_data.get('squareFeet', 0):,} sq ft | 
                    Built {property_data.get('yearBuilt', 'Unknown')}</p>
                    
                    <p><strong>Market:</strong> {prop.get('market', 'Unknown')}</p>
                    
                    <div class="reasons">
                        <strong>Why This Property:</strong>
                        {self._format_reasons_list(analysis.analysis_reasons[:5])}
                    </div>
                    
                    {self._format_opportunity_factors(analysis.opportunity_factors)}
                </div>
            """
        
        # Add footer
        html += f"""
                <div class="footer">
                    <h3>📊 Summary</h3>
                    <p><strong>Properties Found:</strong> {len(properties)}</p>
                    <p><strong>Average ROI:</strong> {sum(p['analysis'].roi_percentage for p in properties)/len(properties):.1f}%</p>
                    <p><strong>Total Investment Opportunity:</strong> ${sum(p['property_data'].get('estimatedValue', 0) for p in properties):,}</p>
                    
                    <h3>🎯 Investment Strategy</h3>
                    <p>These properties match the successful patterns of investors who have built $30M+ portfolios:</p>
                    <ul>
                        <li>Price range optimized for Section 8 returns</li>
                        <li>Property sizes in highest demand (2-4 bedrooms)</li>
                        <li>Markets with strong rental demand</li>
                        <li>Positive cash flow with Fair Market Rent rates</li>
                    </ul>
                    
                    <p><em>This alert was generated by Section 8 Investor Pro monitoring system.</em></p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def _create_daily_summary_email(self, scan_results: Dict[str, Any]) -> str:
        """Create HTML content for daily summary email"""
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Section 8 Daily Summary</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 800px; margin: 0 auto; padding: 20px; }}
                .header {{ background: #34495e; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }}
                .stat-card {{ background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; }}
                .stat-number {{ font-size: 2em; font-weight: bold; color: #2c3e50; }}
                .market-summary {{ margin: 20px 0; }}
                .market {{ padding: 10px; margin: 5px 0; background: #f9f9f9; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📊 Section 8 Investment Daily Summary</h1>
                    <p>Comprehensive market scan results for {datetime.now().strftime('%Y-%m-%d')}</p>
                </div>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">{scan_results.get('markets_scanned', 0)}</div>
                        <div>Markets Scanned</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{scan_results.get('total_properties', 0)}</div>
                        <div>Properties Analyzed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{scan_results.get('high_priority_count', 0)}</div>
                        <div>High Priority Opportunities</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{scan_results.get('total_api_calls', 0)}</div>
                        <div>API Calls Made</div>
                    </div>
                </div>
                
                <div class="market-summary">
                    <h3>🗺️ Market Performance</h3>
        """
        
        # Add market summaries
        market_summaries = scan_results.get('market_summaries', [])
        for market in sorted(market_summaries, key=lambda x: x.get('opportunities_found', 0), reverse=True)[:10]:
            html += f"""
                <div class="market">
                    <strong>{market.get('city')}, {market.get('state')}</strong> 
                    ({market.get('priority')} Priority) - 
                    {market.get('opportunities_found', 0)} opportunities found 
                    ({market.get('high_priority', 0)} high priority)
                </div>
            """
        
        # Add performance metrics
        if scan_results.get('high_priority_count', 0) > 0:
            avg_score = sum(
                prop.get('analysis', {}).get('investment_score', 0) 
                for prop in scan_results.get('high_priority_properties', [])
            ) / len(scan_results.get('high_priority_properties', [1]))
            
            html += f"""
                <h3>📈 Performance Metrics</h3>
                <p><strong>Average Investment Score:</strong> {avg_score:.1f}/100</p>
                <p><strong>Success Rate:</strong> {(scan_results.get('high_priority_count', 0) / max(scan_results.get('total_properties', 1), 1) * 100):.1f}% of properties met high-priority criteria</p>
                <p><strong>Scan Duration:</strong> {scan_results.get('total_scan_time', 0):.1f} seconds</p>
            """
        
        html += """
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def _create_system_alert_email(
        self, 
        alert_type: str, 
        message: str, 
        details: Dict[str, Any] = None
    ) -> str:
        """Create HTML content for system alert email"""
        
        color_map = {
            "error": "#e74c3c",
            "warning": "#f39c12",
            "info": "#3498db",
            "success": "#27ae60"
        }
        
        color = color_map.get(alert_type, "#34495e")
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>System Alert</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: {color}; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                .content {{ background: #f8f9fa; padding: 20px; border-radius: 8px; }}
                .details {{ background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>System Alert: {alert_type.title()}</h1>
                    <p>Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                
                <div class="content">
                    <h3>Message:</h3>
                    <p>{message}</p>
        """
        
        if details:
            html += """
                    <div class="details">
                        <h4>Additional Details:</h4>
            """
            for key, value in details.items():
                html += f"<p><strong>{key.replace('_', ' ').title()}:</strong> {value}</p>"
            html += "</div>"
        
        html += """
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def _create_market_update_email(self, market_summaries: List[Dict[str, Any]]) -> str:
        """Create HTML content for market update email"""
        
        # Calculate totals
        total_properties = sum(m.get('total_properties', 0) for m in market_summaries)
        total_opportunities = sum(m.get('opportunities_found', 0) for m in market_summaries)
        total_high_priority = sum(m.get('high_priority', 0) for m in market_summaries)
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Market Update</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 800px; margin: 0 auto; padding: 20px; }}
                .header {{ background: #16a085; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                .summary {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 20px; }}
                .summary-card {{ background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; }}
                .market-list {{ margin: 20px 0; }}
                .market {{ padding: 15px; margin: 10px 0; background: #f9f9f9; border-radius: 8px; border-left: 4px solid #16a085; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📈 Market Performance Update</h1>
                    <p>Latest scan results across all monitored markets</p>
                    <p><small>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</small></p>
                </div>
                
                <div class="summary">
                    <div class="summary-card">
                        <h3>{len(market_summaries)}</h3>
                        <p>Markets Scanned</p>
                    </div>
                    <div class="summary-card">
                        <h3>{total_properties}</h3>
                        <p>Properties Analyzed</p>
                    </div>
                    <div class="summary-card">
                        <h3>{total_opportunities}</h3>
                        <p>Opportunities Found</p>
                    </div>
                    <div class="summary-card">
                        <h3>{total_high_priority}</h3>
                        <p>High Priority</p>
                    </div>
                </div>
                
                <div class="market-list">
                    <h3>🏙️ Market Performance Details</h3>
        """
        
        # Sort markets by opportunities found
        sorted_markets = sorted(
            market_summaries, 
            key=lambda x: x.get('opportunities_found', 0), 
            reverse=True
        )
        
        for market in sorted_markets:
            success_rate = 0
            if market.get('total_properties', 0) > 0:
                success_rate = (market.get('opportunities_found', 0) / market.get('total_properties', 1)) * 100
            
            html += f"""
                <div class="market">
                    <h4>{market.get('city')}, {market.get('state')} ({market.get('priority')} Priority)</h4>
                    <p><strong>Results:</strong> {market.get('opportunities_found', 0)} opportunities from {market.get('total_properties', 0)} properties analyzed</p>
                    <p><strong>High Priority:</strong> {market.get('high_priority', 0)} | <strong>Success Rate:</strong> {success_rate:.1f}%</p>
                    <p><strong>Scan Time:</strong> {market.get('scan_time', 0):.1f}s | <strong>API Calls:</strong> {market.get('api_calls', 0)}</p>
                </div>
            """
        
        html += """
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def _format_reasons_list(self, reasons: List[str]) -> str:
        """Format analysis reasons as HTML list"""
        if not reasons:
            return "<p>No specific reasons available.</p>"
        
        html = "<ul>"
        for reason in reasons:
            html += f"<li>{reason}</li>"
        html += "</ul>"
        return html
    
    def _format_opportunity_factors(self, factors: List[str]) -> str:
        """Format opportunity factors as HTML"""
        if not factors:
            return ""
        
        html = '<div class="reasons"><strong>Opportunity Factors:</strong><ul>'
        for factor in factors[:3]:  # Limit to top 3
            html += f"<li>{factor}</li>"
        html += "</ul></div>"
        return html
    
    async def test_email_service(self) -> bool:
        """Test email service connectivity"""
        try:
            test_subject = "🧪 Section 8 Monitor Test Email"
            test_content = """
            <h2>Email Service Test</h2>
            <p>This is a test email from your Section 8 property monitoring system.</p>
            <p>If you receive this, your email alerts are working correctly!</p>
            <p><strong>System Status:</strong> ✅ Ready for deployment</p>
            """
            
            success = await self._send_email(
                to_email=self.alert_email,
                subject=test_subject,
                html_content=test_content,
                alert_type="test"
            )
            
            if success:
                self.logger.info("Email service test successful")
            else:
                self.logger.error("Email service test failed")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Email service test error: {str(e)}")
            return False
    
    def get_notification_stats(self) -> Dict[str, Any]:
        """Get notification service statistics"""
        return {
            "emails_sent": self.sent_count,
            "emails_failed": self.failed_count,
            "success_rate": (self.sent_count / max(self.sent_count + self.failed_count, 1)) * 100,
            "configured_email": self.alert_email,
            "service_status": "active" if self.resend_api_key else "not_configured"
        }
    
    async def close(self):
        """Close the HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()
