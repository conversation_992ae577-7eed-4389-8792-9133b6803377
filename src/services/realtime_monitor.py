#!/usr/bin/env python3
"""
Real-time Property Monitor Service
Handles 1-minute interval monitoring with automatic parameter adjustment
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json

from ..core.config import Config, MarketConfig
from ..core.database import DatabaseManager
from ..api.real_estate_client import RealEstateAPIClient
from ..api.hud_client import HUDAPIClient
from ..services.property_analyzer import PropertyAnalyzer
from ..services.notification_service import NotificationService

class RealtimePropertyMonitor:
    """Real-time property monitoring with 1-minute intervals"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.scan_count = 0
        self.last_scan_time = None
        self.next_scan_time = None
        
        # Initialize components
        self.db_manager = DatabaseManager(config.DATABASE_URL)
        self.real_estate_client = RealEstateAPIClient(
            api_key=config.REAL_ESTATE_API_KEY,
            base_url=config.REAL_ESTATE_API_URL
        )
        self.hud_client = HUDAPIClient(
            api_key=config.HUD_API_KEY,
            base_url=config.HUD_API_URL
        )
        self.property_analyzer = PropertyAnalyzer(
            real_estate_client=self.real_estate_client,
            hud_client=self.hud_client,
            config=config
        )
        self.notification_service = NotificationService(config)
        
        # Performance tracking
        self.daily_stats = {
            'properties_analyzed': 0,
            'high_priority_found': 0,
            'alerts_sent': 0,
            'api_calls_made': 0,
            'markets_processed': 0,
            'errors_count': 0
        }
    
    async def initialize(self):
        """Initialize the monitoring service"""
        try:
            await self.db_manager.initialize()
            self.logger.info("Real-time monitor initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize monitor: {str(e)}")
            raise
    
    async def start_monitoring(self):
        """Start the real-time monitoring loop"""
        self.running = True
        self.logger.info("Starting real-time property monitoring (1-minute intervals)")
        
        while self.running:
            try:
                scan_start_time = datetime.now()
                self.last_scan_time = scan_start_time
                self.next_scan_time = scan_start_time + timedelta(minutes=1)
                
                # Perform property scan
                scan_results = await self.perform_scan()
                
                # Update statistics
                self.scan_count += 1
                self.daily_stats['markets_processed'] += len(scan_results.get('markets_scanned', []))
                self.daily_stats['properties_analyzed'] += scan_results.get('total_properties', 0)
                self.daily_stats['high_priority_found'] += scan_results.get('high_priority_count', 0)
                
                # Log scan completion
                scan_duration = (datetime.now() - scan_start_time).total_seconds()
                self.logger.info(
                    f"Scan #{self.scan_count} completed in {scan_duration:.2f}s - "
                    f"Found {scan_results.get('total_properties', 0)} properties, "
                    f"{scan_results.get('high_priority_count', 0)} high priority"
                )
                
                # Wait until next scan time
                await self.wait_for_next_scan()
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {str(e)}")
                self.daily_stats['errors_count'] += 1
                # Wait 30 seconds before retrying on error
                await asyncio.sleep(30)
    
    async def perform_scan(self) -> Dict[str, Any]:
        """Perform a single property scan across all markets"""
        scan_results = {
            'total_properties': 0,
            'high_priority_count': 0,
            'markets_scanned': [],
            'scan_time': datetime.now().isoformat()
        }
        
        # Get active search criteria from database
        active_searches = await self.get_active_search_criteria()
        
        if not active_searches:
            # Fall back to default state-level searches
            active_searches = self.get_default_state_searches()
        
        # Process each search criteria
        for search_criteria in active_searches:
            try:
                market_results = await self.scan_market(search_criteria)
                scan_results['total_properties'] += market_results.get('properties_found', 0)
                scan_results['high_priority_count'] += market_results.get('high_priority_count', 0)
                scan_results['markets_scanned'].append({
                    'state': search_criteria.get('state'),
                    'city': search_criteria.get('city'),
                    'properties_found': market_results.get('properties_found', 0)
                })
                
                # Auto-adjust parameters if no properties found
                if market_results.get('properties_found', 0) == 0:
                    await self.auto_adjust_search_parameters(search_criteria)
                
            except Exception as e:
                self.logger.error(f"Error scanning market {search_criteria}: {str(e)}")
                continue
        
        return scan_results
    
    async def scan_market(self, search_criteria: Dict[str, Any]) -> Dict[str, Any]:
        """Scan a single market for properties"""
        state = search_criteria.get('state')
        city = search_criteria.get('city')
        
        # Build search parameters
        api_criteria = {
            'min_price': search_criteria.get('min_price', 30000),
            'max_price': search_criteria.get('max_price', 300000),
            'bedrooms_min': search_criteria.get('min_bedrooms', 2),
            'bedrooms_max': search_criteria.get('max_bedrooms', 6),
            'bathrooms_min': search_criteria.get('min_bathrooms', 1),
            'property_type': search_criteria.get('property_types', ['SFR', 'MFR']),
            'search_radius': search_criteria.get('search_radius', 75)
        }
        
        # Search for properties
        search_results = await self.real_estate_client.search_section8_properties(
            state=state,
            city=city,
            criteria=api_criteria
        )
        
        if not search_results or not search_results.get('data'):
            return {'properties_found': 0, 'high_priority_count': 0}
        
        properties = search_results['data']
        high_priority_count = 0
        
        # Analyze and save properties
        for property_data in properties:
            try:
                # Analyze property for investment potential
                analysis = await self.property_analyzer.analyze_property(property_data)
                
                if analysis and analysis.get('investment_priority') == 'HIGH':
                    high_priority_count += 1
                    
                    # Send alert for high priority properties
                    await self.send_property_alert(property_data, analysis)
                
                # Save to database
                if analysis:
                    await self.save_property_to_database(property_data, analysis, search_criteria)
                
            except Exception as e:
                self.logger.error(f"Error analyzing property: {str(e)}")
                continue
        
        return {
            'properties_found': len(properties),
            'high_priority_count': high_priority_count
        }
    
    async def auto_adjust_search_parameters(self, search_criteria: Dict[str, Any]):
        """Automatically adjust search parameters when no properties found"""
        adjustments_made = []
        
        # Increase price range
        if search_criteria.get('max_price', 0) < 500000:
            search_criteria['max_price'] = min(search_criteria.get('max_price', 300000) * 1.5, 500000)
            adjustments_made.append(f"Increased max price to {search_criteria['max_price']}")
        
        # Expand bedroom range
        if search_criteria.get('min_bedrooms', 2) > 1:
            search_criteria['min_bedrooms'] = max(search_criteria.get('min_bedrooms', 2) - 1, 1)
            adjustments_made.append(f"Reduced min bedrooms to {search_criteria['min_bedrooms']}")
        
        # Increase search radius
        if search_criteria.get('search_radius', 75) < 150:
            search_criteria['search_radius'] = min(search_criteria.get('search_radius', 75) + 25, 150)
            adjustments_made.append(f"Increased search radius to {search_criteria['search_radius']} miles")
        
        if adjustments_made:
            self.logger.info(f"Auto-adjusted search parameters: {', '.join(adjustments_made)}")
            # Update in database if this is a saved search
            if search_criteria.get('id'):
                await self.update_search_criteria(search_criteria)
    
    async def get_active_search_criteria(self) -> List[Dict[str, Any]]:
        """Get active search criteria from database"""
        try:
            async with self.db_manager.pool.acquire() as conn:
                query = """
                SELECT id, name, state, city, min_price, max_price, min_bedrooms, 
                       max_bedrooms, min_bathrooms, property_types, min_roi, 
                       search_radius, priority
                FROM search_criteria 
                WHERE is_active = true
                ORDER BY priority DESC, created_at ASC
                """
                rows = await conn.fetch(query)
                
                return [
                    {
                        **dict(row),
                        'property_types': json.loads(row['property_types']) if row['property_types'] else ['SFR', 'MFR']
                    }
                    for row in rows
                ]
        except Exception as e:
            self.logger.error(f"Error fetching search criteria: {str(e)}")
            return []
    
    def get_default_state_searches(self) -> List[Dict[str, Any]]:
        """Get default state-level searches from config"""
        markets = self.config.get_high_priority_markets()
        return [
            {
                'state': market.state,
                'city': None,  # State-wide search
                'min_price': 30000,
                'max_price': 300000,
                'min_bedrooms': 2,
                'max_bedrooms': 6,
                'min_bathrooms': 1,
                'property_types': ['SFR', 'MFR'],
                'search_radius': market.search_radius_miles,
                'priority': market.priority
            }
            for market in markets
        ]
    
    async def save_property_to_database(self, property_data: Dict, analysis: Dict, search_criteria: Dict):
        """Save analyzed property to database"""
        try:
            # Prepare property data for database
            db_property = {
                'property_id': property_data.get('id'),
                'address': property_data.get('address'),
                'city': property_data.get('city'),
                'state': property_data.get('state'),
                'zip_code': property_data.get('zip'),
                'bedrooms': property_data.get('bedrooms'),
                'bathrooms': property_data.get('bathrooms'),
                'square_feet': property_data.get('squareFeet'),
                'lot_square_feet': property_data.get('lotSquareFeet'),
                'year_built': property_data.get('yearBuilt'),
                'property_type': property_data.get('propertyType'),
                'estimated_value': property_data.get('estimatedValue'),
                'latitude': property_data.get('latitude'),
                'longitude': property_data.get('longitude'),
                'investment_score': analysis.get('investment_score'),
                'investment_priority': analysis.get('investment_priority'),
                'roi_percentage': analysis.get('roi_percentage'),
                'estimated_cash_flow': analysis.get('estimated_cash_flow'),
                'estimated_rent': analysis.get('estimated_rent'),
                'search_criteria_id': search_criteria.get('id'),
                'raw_data': property_data
            }
            
            await self.db_manager.save_property(db_property)
            
        except Exception as e:
            self.logger.error(f"Error saving property to database: {str(e)}")
    
    async def send_property_alert(self, property_data: Dict, analysis: Dict):
        """Send email alert for high priority property"""
        try:
            await self.notification_service.send_property_alert(property_data, analysis)
            self.daily_stats['alerts_sent'] += 1
        except Exception as e:
            self.logger.error(f"Error sending property alert: {str(e)}")
    
    async def wait_for_next_scan(self):
        """Wait until the next scan time (1 minute intervals)"""
        if self.next_scan_time:
            wait_seconds = (self.next_scan_time - datetime.now()).total_seconds()
            if wait_seconds > 0:
                await asyncio.sleep(wait_seconds)
    
    async def stop_monitoring(self):
        """Stop the monitoring service"""
        self.running = False
        self.logger.info("Real-time monitoring stopped")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        return {
            'running': self.running,
            'scan_count': self.scan_count,
            'last_scan_time': self.last_scan_time.isoformat() if self.last_scan_time else None,
            'next_scan_time': self.next_scan_time.isoformat() if self.next_scan_time else None,
            'daily_stats': self.daily_stats.copy()
        }
    
    async def close(self):
        """Clean up resources"""
        await self.real_estate_client.close()
        await self.db_manager.close()
