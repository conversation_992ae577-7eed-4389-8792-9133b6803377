#!/usr/bin/env python3
"""
HUD API Client for Section 8 Investor Pro
Handles interactions with HUD APIs for Fair Market Rent and Section 8 data
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import json

class HUDAPIClient:
    """Client for HUD APIs with proper authentication"""
    
    def __init__(self, api_key: str, base_url: str):
        """
        Initialize the HUD API client
        
        Args:
            api_key: HUD API key for authentication
            base_url: Base URL for HUD API
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.logger = logging.getLogger(__name__)
        
        # Session for connection pooling
        self.session = None
        
        # Cache for FMR data to reduce API calls
        self.fmr_cache = {}
        self.cache_expiry = {}
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self._get_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            connector = aiohttp.TCPConnector(limit=5, limit_per_host=3)
            
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                headers=self._get_headers()
            )
        
        return self.session
    
    def _get_headers(self) -> Dict[str, str]:
        """
        Get properly formatted headers for HUD API
        HUD API uses Bearer token authentication
        """
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "User-Agent": "Section8InvestorPro/2.0.0",
            "Accept": "application/json"
        }
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        params: Optional[Dict] = None
    ) -> Optional[Dict]:
        """
        Make HTTP request to HUD API
        
        Args:
            method: HTTP method (GET)
            endpoint: API endpoint
            params: URL parameters
        
        Returns:
            API response data or None if request failed
        """
        session = await self._get_session()
        url = f"{self.base_url}{endpoint}"
        
        try:
            self.logger.debug(f"Making {method} request to {url}")
            
            if method.upper() == "GET":
                async with session.get(url, params=params) as response:
                    return await self._handle_response(response, url)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
        except aiohttp.ClientError as e:
            self.logger.error(f"HUD API request failed for {url}: {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error for HUD API {url}: {str(e)}")
            return None
    
    async def _handle_response(self, response: aiohttp.ClientResponse, url: str) -> Optional[Dict]:
        """Handle HUD API response with proper error checking"""
        try:
            if response.status == 200:
                data = await response.json()
                self.logger.debug(f"Successful HUD API request to {url}")
                return data
            elif response.status == 429:
                self.logger.warning(f"HUD API rate limit exceeded for {url}")
                return None
            elif response.status == 401:
                self.logger.error(f"HUD API authentication failed for {url}")
                return None
            else:
                error_text = await response.text()
                self.logger.error(f"HUD API error {response.status} for {url}: {error_text}")
                return None
                
        except aiohttp.ContentTypeError:
            text_response = await response.text()
            self.logger.error(f"Non-JSON response from HUD API {url}: {text_response}")
            return None
        except Exception as e:
            self.logger.error(f"Error handling HUD API response from {url}: {str(e)}")
            return None
    
    async def test_connection(self) -> bool:
        """Test HUD API connection"""
        try:
            # Test with FMR data request for a common area
            result = await self.get_fair_market_rents("MI", "Detroit")
            if result is not None:
                self.logger.info("HUD API connection test successful")
                return True
            else:
                self.logger.error("HUD API connection test failed")
                return False
                
        except Exception as e:
            self.logger.error(f"HUD API connection test error: {str(e)}")
            return False
    
    def _get_cache_key(self, state: str, city: str, year: int = None) -> str:
        """Generate cache key for FMR data"""
        year = year or datetime.now().year
        return f"{state}_{city}_{year}".upper()
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid (24 hours)"""
        if cache_key not in self.cache_expiry:
            return False
        
        expiry_time = self.cache_expiry[cache_key]
        return datetime.now() < expiry_time
    
    async def get_fair_market_rents(
        self, 
        state: str, 
        city: str = None, 
        county: str = None,
        year: int = None
    ) -> Optional[Dict]:
        """
        Get Fair Market Rent data from HUD API
        
        Args:
            state: State code (e.g., 'MI', 'OH')
            city: City name (optional)
            county: County name (optional)
            year: Year for FMR data (defaults to current year)
        
        Returns:
            FMR data dictionary or None if request failed
        """
        year = year or datetime.now().year
        cache_key = self._get_cache_key(state, city or county or "statewide", year)
        
        # Check cache first
        if cache_key in self.fmr_cache and self._is_cache_valid(cache_key):
            self.logger.debug(f"Using cached FMR data for {cache_key}")
            return self.fmr_cache[cache_key]
        
        # Build API endpoint and parameters
        endpoint = f"/fmr/data/{state}"
        params = {"year": year}
        
        if city:
            params["entityname"] = city
        elif county:
            params["entityname"] = county
        
        try:
            result = await self._make_request("GET", endpoint, params=params)
            
            if result and "data" in result:
                # Cache the result
                self.fmr_cache[cache_key] = result
                self.cache_expiry[cache_key] = datetime.now() + timedelta(hours=24)
                
                self.logger.info(f"Retrieved FMR data for {state} {city or county or 'statewide'}")
                return result
            else:
                self.logger.warning(f"No FMR data found for {state} {city or county}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting FMR data: {str(e)}")
            return None
    
    async def get_metro_fmr(self, metro_code: str, year: int = None) -> Optional[Dict]:
        """
        Get FMR data by metropolitan area code
        
        Args:
            metro_code: Metropolitan area code
            year: Year for FMR data
        
        Returns:
            FMR data or None if request failed
        """
        year = year or datetime.now().year
        endpoint = f"/fmr/data/metro/{metro_code}"
        params = {"year": year}
        
        return await self._make_request("GET", endpoint, params=params)
    
    async def get_small_area_fmr(self, zip_code: str, year: int = None) -> Optional[Dict]:
        """
        Get Small Area Fair Market Rent data by ZIP code
        
        Args:
            zip_code: ZIP code
            year: Year for SAFMR data
        
        Returns:
            SAFMR data or None if request failed
        """
        year = year or datetime.now().year
        endpoint = f"/safmr/data/{zip_code}"
        params = {"year": year}
        
        return await self._make_request("GET", endpoint, params=params)
    
    async def get_income_limits(self, state: str, city: str = None, year: int = None) -> Optional[Dict]:
        """
        Get income limits for Section 8 eligibility
        
        Args:
            state: State code
            city: City name (optional)
            year: Year for income limits
        
        Returns:
            Income limits data or None if request failed
        """
        year = year or datetime.now().year
        endpoint = f"/il/data/{state}"
        params = {"year": year}
        
        if city:
            params["entityname"] = city
        
        return await self._make_request("GET", endpoint, params=params)
    
    def extract_bedroom_rents(self, fmr_data: Dict) -> Dict[int, float]:
        """
        Extract bedroom-specific rent amounts from FMR API response
        
        Args:
            fmr_data: Raw FMR API response
        
        Returns:
            Dictionary mapping bedroom count to rent amount
        """
        bedroom_rents = {}
        
        if not fmr_data or "data" not in fmr_data:
            return bedroom_rents
        
        try:
            # Handle different FMR API response formats
            data = fmr_data["data"]
            if isinstance(data, list) and len(data) > 0:
                fmr_info = data[0]  # Use first result
            elif isinstance(data, dict):
                fmr_info = data
            else:
                return bedroom_rents
            
            # Extract bedroom rent amounts
            # Common HUD FMR field names
            rent_fields = {
                0: ["efficiency", "eff", "studio", "0br"],
                1: ["1br", "onebr", "1bedroom"],
                2: ["2br", "twobr", "2bedroom"],
                3: ["3br", "threebr", "3bedroom"],
                4: ["4br", "fourbr", "4bedroom"],
                5: ["5br", "fivebr", "5bedroom"]
            }
            
            for bedrooms, field_names in rent_fields.items():
                for field_name in field_names:
                    if field_name in fmr_info:
                        try:
                            rent_amount = float(fmr_info[field_name])
                            bedroom_rents[bedrooms] = rent_amount
                            break
                        except (ValueError, TypeError):
                            continue
            
            self.logger.debug(f"Extracted bedroom rents: {bedroom_rents}")
            return bedroom_rents
            
        except Exception as e:
            self.logger.error(f"Error extracting bedroom rents: {str(e)}")
            return bedroom_rents
    
    def get_fallback_fmr(self, state: str, bedrooms: int) -> float:
        """
        Get fallback FMR estimates when API data is not available
        Based on national averages and state adjustments
        """
        # National average base rates (conservative estimates)
        base_rates = {
            0: 650,   # Efficiency
            1: 750,   # 1 bedroom
            2: 900,   # 2 bedroom
            3: 1200,  # 3 bedroom
            4: 1500,  # 4 bedroom
            5: 1800   # 5 bedroom
        }
        
        # State adjustment factors (based on general cost of living)
        state_factors = {
            "MI": 0.85,  # Michigan - lower cost
            "OH": 0.80,  # Ohio - lower cost
            "PA": 0.90,  # Pennsylvania - moderate
            "NY": 1.20,  # New York - higher cost (upstate)
            "WI": 0.85,  # Wisconsin - lower cost
            "IN": 0.75,  # Indiana - lower cost
            "AL": 0.70,  # Alabama - lower cost
            "TN": 0.80,  # Tennessee - lower cost
            "MS": 0.65,  # Mississippi - lowest cost
            "LA": 0.75,  # Louisiana - lower cost
            "AR": 0.70,  # Arkansas - lower cost
        }
        
        base_rent = base_rates.get(bedrooms, base_rates[2])  # Default to 2BR
        state_factor = state_factors.get(state.upper(), 0.85)  # Default conservative
        
        estimated_rent = base_rent * state_factor
        
        self.logger.debug(f"Fallback FMR for {state} {bedrooms}BR: ${estimated_rent:.0f}")
        return estimated_rent
    
    async def close(self):
        """Close the HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()
