#!/usr/bin/env python3
"""
Real Estate API Client for Section 8 Investor Pro
Handles all interactions with the Real Estate API including authentication
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import json

class RealEstateAPIClient:
    """Client for Real Estate API with proper authentication and rate limiting"""
    
    def __init__(self, api_key: str, base_url: str, rate_limit_delay: int = 2):
        """
        Initialize the Real Estate API client
        
        Args:
            api_key: API key for authentication
            base_url: Base URL for the API
            rate_limit_delay: Delay between requests in seconds
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.rate_limit_delay = rate_limit_delay
        self.logger = logging.getLogger(__name__)
        
        # Request tracking for rate limiting
        self.last_request_time = datetime.now()
        self.request_count = 0
        
        # Session for connection pooling
        self.session = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self._get_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            # Configure session with proper timeout and headers
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
            
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                headers=self._get_headers()
            )
        
        return self.session
    
    def _get_headers(self) -> Dict[str, str]:
        """
        Get properly formatted headers for Real Estate API
        The API uses Bearer token authentication in the Authorization header
        """
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "User-Agent": "Section8InvestorPro/2.0.0",
            "Accept": "application/json"
        }
    
    async def _rate_limit(self):
        """Implement rate limiting to respect API limits"""
        current_time = datetime.now()
        time_since_last = (current_time - self.last_request_time).total_seconds()
        
        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = datetime.now()
        self.request_count += 1
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Dict] = None,
        params: Optional[Dict] = None
    ) -> Optional[Dict]:
        """
        Make HTTP request to the Real Estate API
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Request body data (for POST requests)
            params: URL parameters (for GET requests)
        
        Returns:
            API response data or None if request failed
        """
        await self._rate_limit()
        
        session = await self._get_session()
        url = f"{self.base_url}{endpoint}"
        
        try:
            self.logger.debug(f"Making {method} request to {url}")
            
            if method.upper() == "POST":
                async with session.post(url, json=data) as response:
                    return await self._handle_response(response, url)
            elif method.upper() == "GET":
                async with session.get(url, params=params) as response:
                    return await self._handle_response(response, url)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
        except aiohttp.ClientError as e:
            self.logger.error(f"Request failed for {url}: {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error for {url}: {str(e)}")
            return None
    
    async def _handle_response(self, response: aiohttp.ClientResponse, url: str) -> Optional[Dict]:
        """Handle API response with proper error checking"""
        try:
            if response.status == 200:
                data = await response.json()
                self.logger.debug(f"Successful request to {url}")
                return data
            elif response.status == 429:
                # Rate limit exceeded
                self.logger.warning(f"Rate limit exceeded for {url}, waiting...")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
                return None
            elif response.status == 401:
                # Authentication error
                self.logger.error(f"Authentication failed for {url} - check API key")
                return None
            else:
                # Other error
                error_text = await response.text()
                self.logger.error(f"API error {response.status} for {url}: {error_text}")
                return None
                
        except aiohttp.ContentTypeError:
            # Response is not JSON
            text_response = await response.text()
            self.logger.error(f"Non-JSON response from {url}: {text_response}")
            return None
        except Exception as e:
            self.logger.error(f"Error handling response from {url}: {str(e)}")
            return None
    
    async def test_connection(self) -> bool:
        """Test API connection with a simple request"""
        try:
            # Test with a minimal property search
            test_data = {
                "city": "DETROIT",
                "state": "MI",
                "limit": 1
            }
            
            result = await self.search_properties(test_data)
            if result is not None:
                self.logger.info("Real Estate API connection test successful")
                return True
            else:
                self.logger.error("Real Estate API connection test failed")
                return False
                
        except Exception as e:
            self.logger.error(f"Real Estate API connection test error: {str(e)}")
            return False
    
    async def search_properties(self, search_criteria: Dict[str, Any]) -> Optional[Dict]:
        """
        Search for properties using the Property Search API

        Args:
            search_criteria: Dictionary containing search parameters

        Returns:
            Search results or None if request failed
        """
        endpoint = "/v2/PropertySearch"
        return await self._make_request("POST", endpoint, data=search_criteria)

    async def search_section8_properties(self, state: str, city: str = None, criteria: Dict[str, Any] = None) -> Optional[Dict]:
        """
        Search for Section 8 investment properties with optimized criteria

        Args:
            state: State abbreviation (e.g., 'MI', 'OH')
            city: Optional city name for more targeted search
            criteria: Optional additional search criteria

        Returns:
            Search results optimized for Section 8 investments
        """
        # Base Section 8 investment criteria
        search_params = {
            "state": state.upper(),
            "bedrooms_min": 2,  # Section 8 minimum requirement
            "bedrooms_max": 6,
            "bathrooms_min": 1,
            "property_type": ["SFR", "MFR"],  # Correct API property types
            "estimated_value_min": 30000,
            "estimated_value_max": 300000,
            "square_feet_min": 700,
            "year_built_min": 1950,
            "limit": 100,
            "sort": "estimated_value",
            "sort_direction": "asc"
        }

        # Add city if specified
        if city:
            search_params["city"] = city
        else:
            # State-level search with increased radius
            search_params["radius"] = 75

        # Merge with additional criteria if provided
        if criteria:
            search_params.update(criteria)

        self.logger.info(f"Searching Section 8 properties in {state}" + (f", {city}" if city else " (state-wide)"))

        return await self.search_properties(search_params)
    
    async def get_property_details(self, property_identifier: Union[int, str, Dict]) -> Optional[Dict]:
        """
        Get detailed property information using Property Detail API
        
        Args:
            property_identifier: Property ID, address, or identification dict
        
        Returns:
            Property details or None if request failed
        """
        endpoint = "/v2/PropertyDetail"
        
        # Handle different identifier types
        if isinstance(property_identifier, int):
            data = {"id": property_identifier}
        elif isinstance(property_identifier, str):
            data = {"address": property_identifier}
        elif isinstance(property_identifier, dict):
            data = property_identifier
        else:
            self.logger.error(f"Invalid property identifier type: {type(property_identifier)}")
            return None
        
        return await self._make_request("POST", endpoint, data=data)
    
    async def get_property_comps(self, property_identifier: Union[int, str, Dict]) -> Optional[Dict]:
        """
        Get property comparables using Property Comps API
        
        Args:
            property_identifier: Property ID, address, or identification dict
        
        Returns:
            Property comparables or None if request failed
        """
        endpoint = "/v2/PropertyComps"
        
        # Handle different identifier types
        if isinstance(property_identifier, int):
            data = {"id": property_identifier}
        elif isinstance(property_identifier, str):
            data = {"address": property_identifier}
        elif isinstance(property_identifier, dict):
            data = property_identifier
        else:
            self.logger.error(f"Invalid property identifier type: {type(property_identifier)}")
            return None
        
        return await self._make_request("POST", endpoint, data=data)
    
    async def get_property_avm(self, property_identifier: Union[int, str, Dict]) -> Optional[Dict]:
        """
        Get automated valuation model data using Property AVM API
        
        Args:
            property_identifier: Property ID, address, or identification dict
        
        Returns:
            AVM data or None if request failed
        """
        endpoint = "/v2/PropertyAvm"
        
        # Handle different identifier types
        if isinstance(property_identifier, int):
            data = {"id": property_identifier}
        elif isinstance(property_identifier, str):
            data = {"address": property_identifier}
        elif isinstance(property_identifier, dict):
            data = property_identifier
        else:
            self.logger.error(f"Invalid property identifier type: {type(property_identifier)}")
            return None
        
        return await self._make_request("POST", endpoint, data=data)
    
    async def verify_address(self, addresses: List[Dict[str, str]]) -> Optional[Dict]:
        """
        Verify addresses using Address Verification API
        
        Args:
            addresses: List of address dictionaries
        
        Returns:
            Verification results or None if request failed
        """
        endpoint = "/v2/AddressVerification"
        data = {"addresses": addresses}
        
        return await self._make_request("POST", endpoint, data=data)
    
    async def autocomplete_address(self, search_text: str, search_types: List[str]) -> Optional[Dict]:
        """
        Get address suggestions using AutoComplete API
        
        Args:
            search_text: Partial address text
            search_types: List of search type codes
        
        Returns:
            Address suggestions or None if request failed
        """
        endpoint = "/v2/AutoComplete"
        data = {
            "input": {
                "search": search_text,
                "search_types": search_types
            }
        }
        
        return await self._make_request("POST", endpoint, data=data)
    
    async def get_property_boundary(self, property_identifier: Union[str, Dict]) -> Optional[Dict]:
        """
        Get property boundary using Property Parcel API
        
        Args:
            property_identifier: Address string or identification dict
        
        Returns:
            Property boundary data or None if request failed
        """
        endpoint = "/v1/PropertyParcel"
        
        if isinstance(property_identifier, str):
            data = {"address": property_identifier}
        elif isinstance(property_identifier, dict):
            data = property_identifier
        else:
            self.logger.error(f"Invalid property identifier type: {type(property_identifier)}")
            return None
        
        return await self._make_request("POST", endpoint, data=data)
    
    async def skip_trace(self, address_info: Dict[str, str]) -> Optional[Dict]:
        """
        Perform skip tracing using SkipTrace API
        
        Args:
            address_info: Dictionary with address and optional owner information
        
        Returns:
            Skip trace results or None if request failed
        """
        endpoint = "/v1/SkipTrace"
        return await self._make_request("POST", endpoint, data=address_info)
    
    async def get_property_liens(self, property_identifier: Union[int, str, Dict]) -> Optional[Dict]:
        """
        Get property liens using Involuntary Liens API
        
        Args:
            property_identifier: Property ID, address, or identification dict
        
        Returns:
            Lien information or None if request failed
        """
        endpoint = "/v2/Reports/PropertyLiens"
        
        if isinstance(property_identifier, int):
            data = {"id": property_identifier}
        elif isinstance(property_identifier, str):
            data = {"address": property_identifier}
        elif isinstance(property_identifier, dict):
            data = property_identifier
        else:
            self.logger.error(f"Invalid property identifier type: {type(property_identifier)}")
            return None
        
        return await self._make_request("POST", endpoint, data=data)
    
    def get_request_stats(self) -> Dict[str, Any]:
        """Get API request statistics"""
        return {
            "total_requests": self.request_count,
            "last_request_time": self.last_request_time.isoformat(),
            "rate_limit_delay": self.rate_limit_delay
        }
    
    async def close(self):
        """Close the HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()
