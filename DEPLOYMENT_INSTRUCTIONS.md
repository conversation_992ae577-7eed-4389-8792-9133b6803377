
# Section 8 Property Monitor - PythonAnywhere Deployment Instructions

## 1. Upload Files
Upload all project files to your PythonAnywhere account:
- production_monitor.py
- config.json
- .env
- requirements.txt
- run_monitor.py
- wsgi.py
- dashboard/ (entire folder)

## 2. Set Up Scheduled Task
1. Go to PythonAnywhere Dashboard > Tasks
2. Create a new scheduled task
3. Set command: python3.10 /home/<USER>/section8-monitor/run_monitor.py
4. Set schedule: Every 1 minute
5. Enable the task

## 3. Set Up Web App (Optional)
1. Go to PythonAnywhere Dashboard > Web
2. Create a new web app
3. Choose Manual configuration
4. Set source code directory: /home/<USER>/section8-monitor
5. Set WSGI configuration file: /home/<USER>/section8-monitor/wsgi.py
6. Reload the web app

## 4. Environment Variables
Make sure these are set in your .env file:
- DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
- RESEND_API_KEY=re_Yp76vKMM_8W3kEDTidZSCJ77Uc3yiyZgP
- ALERT_EMAIL=<EMAIL>
- REAL_ESTATE_API_KEY=AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914
- REAL_ESTATE_API_URL=https://api.realestateapi.com

## 5. Test the Setup
1. Run the monitor manually first: python3.10 production_monitor.py
2. Check logs for any errors
3. Verify database connection
4. Test email notifications

## 6. Monitor Performance
- Check PythonAnywhere logs regularly
- Monitor email alerts
- Verify database is being populated
- Check for any API rate limiting issues

## 7. Troubleshooting
- If tasks fail, check the error logs in PythonAnywhere
- Ensure all dependencies are installed
- Verify database connection string
- Check API keys are valid
- Monitor CPU seconds usage (PythonAnywhere limits)

## 8. Scaling Considerations
- Monitor API call limits
- Adjust search frequency if needed
- Consider upgrading PythonAnywhere plan for more CPU seconds
- Implement error recovery mechanisms
