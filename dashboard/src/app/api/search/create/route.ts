import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/database';

interface SearchCriteria {
  name: string;
  state: string;
  city?: string;
  min_price: number;
  max_price: number;
  min_bedrooms: number;
  max_bedrooms: number;
  min_bathrooms: number;
  property_types: string[];
  min_roi: number;
  search_radius: number;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
}

export async function POST(request: NextRequest) {
  try {
    const body: SearchCriteria = await request.json();
    
    // Validate required fields
    if (!body.name || !body.state) {
      return NextResponse.json(
        {
          success: false,
          error: 'Name and state are required fields',
        },
        { status: 400 }
      );
    }

    // Validate property types
    if (!body.property_types || body.property_types.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'At least one property type must be selected',
        },
        { status: 400 }
      );
    }

    const client = await pool.connect();
    
    try {
      // Insert the search criteria into the database
      const insertQuery = `
        INSERT INTO search_criteria (
          name, state, city, min_price, max_price, min_bedrooms, max_bedrooms,
          min_bathrooms, property_types, min_roi, search_radius, priority,
          is_active, created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, true, NOW(), NOW()
        )
        RETURNING id, name, state, city, priority, created_at
      `;

      const values = [
        body.name,
        body.state.toUpperCase(),
        body.city || null,
        body.min_price,
        body.max_price,
        body.min_bedrooms,
        body.max_bedrooms,
        body.min_bathrooms,
        JSON.stringify(body.property_types),
        body.min_roi,
        body.search_radius,
        body.priority,
      ];

      const result = await client.query(insertQuery, values);
      const newSearch = result.rows[0];

      // Log the search creation
      const logQuery = `
        INSERT INTO search_history (
          search_criteria_id, action, details, created_at
        ) VALUES (
          $1, 'CREATED', $2, NOW()
        )
      `;

      await client.query(logQuery, [
        newSearch.id,
        JSON.stringify({
          message: 'Search criteria created',
          criteria: body,
        }),
      ]);

      return NextResponse.json({
        success: true,
        data: {
          id: newSearch.id,
          name: newSearch.name,
          state: newSearch.state,
          city: newSearch.city,
          priority: newSearch.priority,
          created_at: newSearch.created_at,
        },
        message: 'Search created successfully',
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error creating search:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create search criteria',
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const client = await pool.connect();
    
    try {
      // Get all active search criteria
      const query = `
        SELECT 
          id, name, state, city, min_price, max_price, min_bedrooms, max_bedrooms,
          min_bathrooms, property_types, min_roi, search_radius, priority,
          is_active, created_at, updated_at,
          (
            SELECT COUNT(*) 
            FROM property_leads pl 
            WHERE pl.search_criteria_id = sc.id 
            AND pl.discovered_at >= NOW() - INTERVAL '7 days'
          ) as recent_properties_found
        FROM search_criteria sc
        WHERE is_active = true
        ORDER BY priority DESC, created_at DESC
      `;

      const result = await client.query(query);
      const searches = result.rows.map(row => ({
        ...row,
        property_types: JSON.parse(row.property_types),
      }));

      return NextResponse.json({
        success: true,
        data: searches,
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error fetching searches:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch search criteria',
      },
      { status: 500 }
    );
  }
}
