import { NextRequest, NextResponse } from 'next/server';

async function testRealEstateAPI(): Promise<boolean> {
  try {
    const apiKey = process.env.REAL_ESTATE_API_KEY;
    const apiUrl = process.env.REAL_ESTATE_API_URL;
    
    if (!apiKey || !apiUrl) {
      return false;
    }

    // Test with a simple property search
    const response = await fetch(`${apiUrl}/v2/PropertySearch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        state: 'MI',
        limit: 1,
      }),
    });

    return response.ok;
  } catch (error) {
    console.error('Real Estate API test failed:', error);
    return false;
  }
}

async function testHUDAPI(): Promise<boolean> {
  try {
    const apiKey = process.env.HUD_API_KEY;
    const apiUrl = process.env.HUD_API_URL;
    
    if (!apiKey || !apiUrl) {
      return false;
    }

    // Test with a simple FMR lookup
    const response = await fetch(`${apiUrl}/fmr/state/MI`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
      },
    });

    return response.ok;
  } catch (error) {
    console.error('HUD API test failed:', error);
    return false;
  }
}

async function testEmailService(): Promise<boolean> {
  try {
    const apiKey = process.env.RESEND_API_KEY;
    
    if (!apiKey) {
      return false;
    }

    // Test with Resend API status endpoint
    const response = await fetch('https://api.resend.com/domains', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
      },
    });

    return response.ok;
  } catch (error) {
    console.error('Email service test failed:', error);
    return false;
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('Testing API connections...');
    
    // Test all APIs in parallel
    const [realEstateStatus, hudStatus, emailStatus] = await Promise.all([
      testRealEstateAPI(),
      testHUDAPI(),
      testEmailService(),
    ]);

    const apiStatus = {
      real_estate_api: realEstateStatus,
      hud_api: hudStatus,
      email_service: emailStatus,
    };

    console.log('API test results:', apiStatus);

    return NextResponse.json({
      success: true,
      data: {
        api_status: apiStatus,
      },
      message: 'API connections tested successfully',
    });

  } catch (error) {
    console.error('Error testing API connections:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to test API connections',
      },
      { status: 500 }
    );
  }
}
