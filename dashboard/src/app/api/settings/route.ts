import { NextRequest, NextResponse } from 'next/server';

interface SystemSettings {
  scan_interval: number;
  email_notifications: boolean;
  alert_email: string;
  max_properties_per_scan: number;
  auto_adjust_parameters: boolean;
  min_bedrooms_global: number;
  api_status: {
    real_estate_api: boolean;
    hud_api: boolean;
    email_service: boolean;
  };
}

// In a real application, these would be stored in a database or configuration service
let systemSettings: SystemSettings = {
  scan_interval: 1, // 1 minute
  email_notifications: true,
  alert_email: process.env.ALERT_EMAIL || '<EMAIL>',
  max_properties_per_scan: 100,
  auto_adjust_parameters: true,
  min_bedrooms_global: 2,
  api_status: {
    real_estate_api: !!process.env.REAL_ESTATE_API_KEY,
    hud_api: !!process.env.HUD_API_KEY,
    email_service: !!process.env.RESEND_API_KEY,
  }
};

export async function GET(request: NextRequest) {
  try {
    // Update API status based on current environment variables
    systemSettings.api_status = {
      real_estate_api: !!process.env.REAL_ESTATE_API_KEY,
      hud_api: !!process.env.HUD_API_KEY,
      email_service: !!process.env.RESEND_API_KEY,
    };

    return NextResponse.json({
      success: true,
      data: systemSettings,
    });

  } catch (error) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch system settings',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the settings
    const validatedSettings: Partial<SystemSettings> = {};
    
    if (typeof body.scan_interval === 'number' && body.scan_interval >= 1 && body.scan_interval <= 60) {
      validatedSettings.scan_interval = body.scan_interval;
    }
    
    if (typeof body.email_notifications === 'boolean') {
      validatedSettings.email_notifications = body.email_notifications;
    }
    
    if (typeof body.alert_email === 'string' && body.alert_email.includes('@')) {
      validatedSettings.alert_email = body.alert_email;
    }
    
    if (typeof body.max_properties_per_scan === 'number' && body.max_properties_per_scan >= 10 && body.max_properties_per_scan <= 1000) {
      validatedSettings.max_properties_per_scan = body.max_properties_per_scan;
    }
    
    if (typeof body.auto_adjust_parameters === 'boolean') {
      validatedSettings.auto_adjust_parameters = body.auto_adjust_parameters;
    }
    
    if (typeof body.min_bedrooms_global === 'number' && body.min_bedrooms_global >= 1 && body.min_bedrooms_global <= 5) {
      validatedSettings.min_bedrooms_global = body.min_bedrooms_global;
    }

    // Update the settings
    systemSettings = { ...systemSettings, ...validatedSettings };

    // In a real application, you would save these to a database
    // For now, we'll just keep them in memory
    
    return NextResponse.json({
      success: true,
      data: systemSettings,
      message: 'Settings updated successfully',
    });

  } catch (error) {
    console.error('Error updating settings:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update system settings',
      },
      { status: 500 }
    );
  }
}
