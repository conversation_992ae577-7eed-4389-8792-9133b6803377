import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/database';
import { MarketData } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const daysBack = parseInt(searchParams.get('days_back') || '7');

    const client = await pool.connect();
    
    try {
      // Get market performance data from properties and markets tables
      const marketQuery = `
        SELECT
          COALESCE(m.city, p.city) as city,
          COALESCE(m.state, p.state) as state,
          COALESCE(m.priority, 'MEDIUM') as priority,
          COUNT(p.id) as properties_found,
          COUNT(p.id) FILTER (WHERE p.investment_priority = 'HIGH') as high_priority_count,
          COUNT(p.id) FILTER (WHERE p.investment_priority = 'MEDIUM') as medium_priority_count,
          COALESCE(AVG(p.roi_percentage), 0) as avg_roi,
          COALESCE(MAX(p.first_discovered), MAX(m.last_scanned), NOW()) as last_scanned
        FROM markets m
        FULL OUTER JOIN properties p ON (m.city = p.city AND m.state = p.state)
        WHERE p.first_discovered >= NOW() - INTERVAL '${daysBack} days' OR m.last_scanned >= NOW() - INTERVAL '${daysBack} days'
        GROUP BY COALESCE(m.city, p.city), COALESCE(m.state, p.state), m.priority
        ORDER BY properties_found DESC, high_priority_count DESC
      `;

      const result = await pool.query(marketQuery);
      
      const markets: MarketData[] = result.rows.map(row => ({
        city: row.city || 'Unknown',
        state: row.state || 'Unknown',
        priority: row.priority || 'MEDIUM',
        properties_found: parseInt(row.properties_found) || 0,
        high_priority_count: parseInt(row.high_priority_count) || 0,
        medium_priority_count: parseInt(row.medium_priority_count) || 0,
        avg_roi: parseFloat(row.avg_roi) || 0,
        last_scanned: row.last_scanned ? row.last_scanned.toISOString() : new Date().toISOString(),
      }));

      return NextResponse.json({
        success: true,
        data: markets,
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error fetching market data:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch market data',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // This could be used to add/update market configurations
    // Implementation depends on requirements
    
    return NextResponse.json({
      success: true,
      message: 'Market configuration updated successfully',
    });

  } catch (error) {
    console.error('Error updating market configuration:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update market configuration',
      },
      { status: 500 }
    );
  }
}
