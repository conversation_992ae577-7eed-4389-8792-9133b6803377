import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const range = searchParams.get('range') || '7d';
    
    // Convert range to days
    const daysMap: { [key: string]: number } = {
      '1d': 1,
      '7d': 7,
      '30d': 30,
      '90d': 90
    };
    
    const days = daysMap[range] || 7;
    
    const client = await pool.connect();
    
    try {
      // Get daily statistics
      const dailyStatsQuery = `
        SELECT 
          DATE(first_discovered) as date,
          COUNT(*) as properties_found,
          COUNT(*) FILTER (WHERE investment_priority = 'HIGH') as high_priority_count,
          COALESCE(AVG(roi_percentage), 0) as avg_roi,
          COUNT(DISTINCT state) as markets_scanned
        FROM properties
        WHERE first_discovered >= NOW() - INTERVAL '${days} days'
        GROUP BY DATE(first_discovered)
        ORDER BY date DESC
      `;
      
      const dailyStatsResult = await client.query(dailyStatsQuery);
      
      // Get market performance by state
      const marketPerformanceQuery = `
        SELECT 
          state,
          COUNT(*) as total_properties,
          COUNT(*) FILTER (WHERE investment_priority = 'HIGH') as high_priority_count,
          COALESCE(AVG(roi_percentage), 0) as avg_roi,
          (
            SELECT city 
            FROM properties p2 
            WHERE p2.state = p1.state 
            AND p2.first_discovered >= NOW() - INTERVAL '${days} days'
            GROUP BY city 
            ORDER BY COUNT(*) DESC 
            LIMIT 1
          ) as best_city
        FROM properties p1
        WHERE first_discovered >= NOW() - INTERVAL '${days} days'
        GROUP BY state
        ORDER BY total_properties DESC
      `;
      
      const marketPerformanceResult = await client.query(marketPerformanceQuery);
      
      // Get investment trends
      const trendsQuery = `
        SELECT 
          COALESCE(AVG(estimated_value), 0) as avg_price_trend,
          COALESCE(AVG(roi_percentage), 0) as avg_roi_trend,
          CASE 
            WHEN COUNT(*) > 0 
            THEN (COUNT(*) FILTER (WHERE investment_priority = 'HIGH')::float / COUNT(*) * 100)
            ELSE 0 
          END as high_priority_percentage
        FROM properties
        WHERE first_discovered >= NOW() - INTERVAL '${days} days'
      `;
      
      const trendsResult = await client.query(trendsQuery);
      
      // Format the response
      const analytics = {
        daily_stats: dailyStatsResult.rows.map(row => ({
          date: row.date,
          properties_found: parseInt(row.properties_found),
          high_priority_count: parseInt(row.high_priority_count),
          avg_roi: parseFloat(row.avg_roi),
          markets_scanned: parseInt(row.markets_scanned)
        })),
        market_performance: marketPerformanceResult.rows.map(row => ({
          state: row.state,
          total_properties: parseInt(row.total_properties),
          high_priority_count: parseInt(row.high_priority_count),
          avg_roi: parseFloat(row.avg_roi),
          best_city: row.best_city || 'Unknown'
        })),
        investment_trends: {
          avg_price_trend: parseFloat(trendsResult.rows[0]?.avg_price_trend || 0),
          avg_roi_trend: parseFloat(trendsResult.rows[0]?.avg_roi_trend || 0),
          high_priority_percentage: parseFloat(trendsResult.rows[0]?.high_priority_percentage || 0)
        }
      };

      return NextResponse.json({
        success: true,
        data: analytics,
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch analytics data',
      },
      { status: 500 }
    );
  }
}
