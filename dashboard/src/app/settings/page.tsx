'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import LoadingSpinner from '@/components/LoadingSpinner';

interface SystemSettings {
  scan_interval: number;
  email_notifications: boolean;
  alert_email: string;
  max_properties_per_scan: number;
  auto_adjust_parameters: boolean;
  min_bedrooms_global: number;
  api_status: {
    real_estate_api: boolean;
    hud_api: boolean;
    email_service: boolean;
  };
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const fetchSettings = async () => {
    try {
      setError(null);
      
      const response = await fetch('/api/settings');
      const data = await response.json();
      
      if (data.success) {
        setSettings(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch settings');
      }

    } catch (err) {
      console.error('Error fetching settings:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    if (!settings) return;
    
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Settings saved successfully!');
      } else {
        throw new Error(data.error || 'Failed to save settings');
      }

    } catch (err) {
      console.error('Error saving settings:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setSaving(false);
    }
  };

  const testApiConnections = async () => {
    try {
      setError(null);
      
      const response = await fetch('/api/settings/test-apis', {
        method: 'POST',
      });

      const data = await response.json();

      if (data.success) {
        setSettings(prev => prev ? {
          ...prev,
          api_status: data.data.api_status
        } : null);
        setSuccess('API connections tested successfully!');
      } else {
        throw new Error(data.error || 'Failed to test API connections');
      }

    } catch (err) {
      console.error('Error testing APIs:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const handleInputChange = (field: keyof SystemSettings, value: any) => {
    setSettings(prev => prev ? { ...prev, [field]: value } : null);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header onRefresh={fetchSettings} refreshing={loading} />
      
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">System Settings</h1>
          <p className="text-gray-600 mt-2">
            Configure monitoring parameters and system behavior
          </p>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="text-red-800">
              <strong>Error:</strong> {error}
            </div>
          </div>
        )}

        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="text-green-800">
              <strong>Success:</strong> {success}
            </div>
          </div>
        )}

        {settings && (
          <div className="space-y-8">
            {/* Monitoring Settings */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Monitoring Settings</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Scan Interval (minutes)
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="60"
                    value={settings.scan_interval}
                    onChange={(e) => handleInputChange('scan_interval', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    How often to scan for new properties (1-60 minutes)
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Max Properties Per Scan
                  </label>
                  <input
                    type="number"
                    min="10"
                    max="1000"
                    value={settings.max_properties_per_scan}
                    onChange={(e) => handleInputChange('max_properties_per_scan', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Maximum properties to analyze per scan
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Minimum Bedrooms (Global)
                  </label>
                  <select
                    value={settings.min_bedrooms_global}
                    onChange={(e) => handleInputChange('min_bedrooms_global', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value={1}>1 Bedroom</option>
                    <option value={2}>2 Bedrooms</option>
                    <option value={3}>3 Bedrooms</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    Section 8 typically requires minimum 2 bedrooms
                  </p>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="auto_adjust"
                    checked={settings.auto_adjust_parameters}
                    onChange={(e) => handleInputChange('auto_adjust_parameters', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="auto_adjust" className="ml-2 text-sm text-gray-700">
                    Auto-adjust search parameters when no properties found
                  </label>
                </div>
              </div>
            </div>

            {/* Email Notifications */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Email Notifications</h2>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="email_notifications"
                    checked={settings.email_notifications}
                    onChange={(e) => handleInputChange('email_notifications', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="email_notifications" className="ml-2 text-sm text-gray-700">
                    Enable email notifications for high priority properties
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Alert Email Address
                  </label>
                  <input
                    type="email"
                    value={settings.alert_email}
                    onChange={(e) => handleInputChange('alert_email', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
            </div>

            {/* API Status */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">API Status</h2>
                <button
                  onClick={testApiConnections}
                  className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  Test Connections
                </button>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Real Estate API</span>
                  <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                    settings.api_status.real_estate_api 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {settings.api_status.real_estate_api ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">HUD API</span>
                  <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                    settings.api_status.hud_api 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {settings.api_status.hud_api ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Email Service</span>
                  <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                    settings.api_status.email_service 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {settings.api_status.email_service ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
              </div>
            </div>

            {/* Save Button */}
            <div className="flex items-center justify-end space-x-4">
              <button
                onClick={() => window.history.back()}
                className="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={saveSettings}
                disabled={saving}
                className="px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {saving && <LoadingSpinner size="small" className="mr-2" />}
                Save Settings
              </button>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
