'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Property } from '@/types';
import { formatCurrency, formatPercentage, formatRelativeTime } from '@/lib/utils';
import Header from '@/components/Header';
import LoadingSpinner from '@/components/LoadingSpinner';

interface PropertyDetail extends Property {
  zillow_data?: {
    zestimate?: number;
    rent_estimate?: number;
    price_history?: Array<{
      date: string;
      price: number;
      event: string;
    }>;
    neighborhood_info?: {
      walk_score?: number;
      school_rating?: number;
      crime_rating?: string;
    };
  };
  real_estate_api_data?: {
    owner_info?: {
      name: string;
      mailing_address: string;
      years_owned: number;
    };
    tax_info?: {
      assessed_value: number;
      tax_amount: number;
      tax_year: number;
    };
    comparable_properties?: Array<{
      address: string;
      distance: number;
      sale_price: number;
      sale_date: string;
    }>;
  };
}

export default function PropertyDetailPage() {
  const params = useParams();
  const propertyId = params.id as string;
  
  const [property, setProperty] = useState<PropertyDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [enhancedDataLoading, setEnhancedDataLoading] = useState(false);

  const fetchPropertyDetail = async () => {
    try {
      setError(null);
      
      const response = await fetch(`/api/properties/${propertyId}`);
      const data = await response.json();
      
      if (data.success) {
        setProperty(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch property details');
      }

    } catch (err) {
      console.error('Error fetching property details:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const fetchEnhancedData = async () => {
    if (!property) return;
    
    setEnhancedDataLoading(true);
    try {
      const response = await fetch(`/api/properties/${propertyId}/enhanced`);
      const data = await response.json();
      
      if (data.success) {
        setProperty(prev => prev ? { ...prev, ...data.data } : null);
      }
    } catch (err) {
      console.error('Error fetching enhanced data:', err);
    } finally {
      setEnhancedDataLoading(false);
    }
  };

  useEffect(() => {
    if (propertyId) {
      fetchPropertyDetail();
    }
  }, [propertyId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error || !property) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="text-red-600 text-xl mb-4">⚠️ Property Not Found</div>
            <p className="text-gray-600 mb-4">{error || 'The requested property could not be found.'}</p>
            <button
              onClick={() => window.history.back()}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Go Back
            </button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{property.address}</h1>
              <p className="text-gray-600 mt-1">{property.city}, {property.state} {property.zip_code}</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(property.estimated_value)}
              </div>
              <div className="text-sm text-gray-500">
                Found {formatRelativeTime(property.discovered_at)}
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Property Info */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Details */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Property Details</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <div className="text-sm text-gray-500">Bedrooms</div>
                  <div className="text-lg font-medium">{property.bedrooms}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Bathrooms</div>
                  <div className="text-lg font-medium">{property.bathrooms}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Square Feet</div>
                  <div className="text-lg font-medium">{property.square_feet?.toLocaleString()}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Year Built</div>
                  <div className="text-lg font-medium">{property.year_built}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Property Type</div>
                  <div className="text-lg font-medium">{property.property_type}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Lot Size</div>
                  <div className="text-lg font-medium">{property.lot_size?.toLocaleString()} sq ft</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Days on Market</div>
                  <div className="text-lg font-medium">{property.days_on_market}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Investment Score</div>
                  <div className="text-lg font-medium text-blue-600">{property.investment_score}/100</div>
                </div>
              </div>
            </div>

            {/* Financial Analysis */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Financial Analysis</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <div className="text-sm text-gray-500">Estimated Rent</div>
                  <div className="text-xl font-bold text-green-600">
                    {formatCurrency(property.estimated_rent)}/mo
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">ROI Percentage</div>
                  <div className="text-xl font-bold text-blue-600">
                    {formatPercentage(property.roi_percentage)}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Monthly Cash Flow</div>
                  <div className="text-xl font-bold text-purple-600">
                    {formatCurrency(property.cash_flow)}
                  </div>
                </div>
              </div>
              
              <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-500">FMR 2BR</div>
                  <div className="text-lg font-medium">{formatCurrency(property.fmr_2br)}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">FMR 3BR</div>
                  <div className="text-lg font-medium">{formatCurrency(property.fmr_3br)}</div>
                </div>
              </div>
            </div>

            {/* Enhanced Data Section */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">Enhanced Property Data</h2>
                <button
                  onClick={fetchEnhancedData}
                  disabled={enhancedDataLoading}
                  className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {enhancedDataLoading && <LoadingSpinner size="small" className="mr-2" />}
                  {enhancedDataLoading ? 'Loading...' : 'Load Zillow Data'}
                </button>
              </div>
              
              {property.zillow_data ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-gray-500">Zestimate</div>
                      <div className="text-lg font-medium">
                        {property.zillow_data.zestimate ? formatCurrency(property.zillow_data.zestimate) : 'N/A'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">Rent Estimate</div>
                      <div className="text-lg font-medium">
                        {property.zillow_data.rent_estimate ? formatCurrency(property.zillow_data.rent_estimate) : 'N/A'}
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-gray-500 text-center py-8">
                  Click "Load Zillow Data" to fetch enhanced property information
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Investment Priority */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Investment Priority</h3>
              <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${
                property.investment_priority === 'HIGH' 
                  ? 'bg-red-100 text-red-800'
                  : property.investment_priority === 'MEDIUM'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-green-100 text-green-800'
              }`}>
                {property.investment_priority} PRIORITY
              </div>
              <div className="mt-4">
                <div className="text-sm text-gray-500">Investment Score</div>
                <div className="text-2xl font-bold text-blue-600">{property.investment_score}/100</div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${property.investment_score}%` }}
                  ></div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button className="w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors">
                  Add to Favorites
                </button>
                <button className="w-full px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                  Schedule Viewing
                </button>
                <button className="w-full px-4 py-2 text-sm font-medium text-green-600 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                  Contact Agent
                </button>
                <button className="w-full px-4 py-2 text-sm font-medium text-purple-600 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                  Run Analysis
                </button>
              </div>
            </div>

            {/* Map Placeholder */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Location</h3>
              <div className="bg-gray-100 rounded-lg h-48 flex items-center justify-center">
                <div className="text-gray-500 text-center">
                  <div className="text-lg mb-2">📍</div>
                  <div>Interactive map coming soon</div>
                  <div className="text-sm mt-1">{property.city}, {property.state}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
