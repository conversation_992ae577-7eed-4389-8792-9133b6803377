'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import LoadingSpinner from '@/components/LoadingSpinner';

interface AnalyticsData {
  daily_stats: Array<{
    date: string;
    properties_found: number;
    high_priority_count: number;
    avg_roi: number;
    markets_scanned: number;
  }>;
  market_performance: Array<{
    state: string;
    total_properties: number;
    high_priority_count: number;
    avg_roi: number;
    best_city: string;
  }>;
  investment_trends: {
    avg_price_trend: number;
    avg_roi_trend: number;
    high_priority_percentage: number;
  };
}

export default function AnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('7d');

  const fetchAnalytics = async () => {
    try {
      setError(null);
      
      const response = await fetch(`/api/analytics?range=${timeRange}`);
      const data = await response.json();
      
      if (data.success) {
        setAnalytics(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch analytics');
      }

    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header onRefresh={fetchAnalytics} refreshing={loading} />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
              <p className="text-gray-600 mt-2">
                Performance insights and market trends
              </p>
            </div>
            
            {/* Time Range Selector */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Time Range
              </label>
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="1d">Last 24 Hours</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 90 Days</option>
              </select>
            </div>
          </div>
        </div>

        {error ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
            <div className="text-red-800">
              <strong>Error:</strong> {error}
            </div>
          </div>
        ) : analytics ? (
          <div className="space-y-8">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="text-sm text-gray-500">Total Properties Found</div>
                <div className="text-2xl font-bold text-blue-600">
                  {analytics.daily_stats.reduce((sum, day) => sum + day.properties_found, 0)}
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  Last {timeRange === '1d' ? '24 hours' : timeRange.replace('d', ' days')}
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="text-sm text-gray-500">High Priority Properties</div>
                <div className="text-2xl font-bold text-red-600">
                  {analytics.daily_stats.reduce((sum, day) => sum + day.high_priority_count, 0)}
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  {analytics.investment_trends.high_priority_percentage.toFixed(1)}% of total
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="text-sm text-gray-500">Average ROI</div>
                <div className="text-2xl font-bold text-green-600">
                  {analytics.investment_trends.avg_roi_trend.toFixed(1)}%
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  Across all properties
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="text-sm text-gray-500">Markets Monitored</div>
                <div className="text-2xl font-bold text-purple-600">
                  {analytics.market_performance.length}
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  Active states
                </div>
              </div>
            </div>

            {/* Daily Performance Chart */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Daily Performance</h2>
              <div className="space-y-4">
                {analytics.daily_stats.map((day, index) => (
                  <div key={day.date} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                    <div>
                      <div className="font-medium text-gray-900">
                        {new Date(day.date).toLocaleDateString()}
                      </div>
                      <div className="text-sm text-gray-500">
                        {day.markets_scanned} markets scanned
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-blue-600">
                        {day.properties_found} properties
                      </div>
                      <div className="text-sm text-red-600">
                        {day.high_priority_count} high priority
                      </div>
                      <div className="text-xs text-green-600">
                        {day.avg_roi.toFixed(1)}% avg ROI
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Market Performance */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Market Performance by State</h2>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        State
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total Properties
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        High Priority
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Avg ROI
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Best City
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {analytics.market_performance.map((market) => (
                      <tr key={market.state}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {market.state}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {market.total_properties}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                          {market.high_priority_count}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                          {market.avg_roi.toFixed(1)}%
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {market.best_city}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Investment Trends */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Investment Trends</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    ${analytics.investment_trends.avg_price_trend.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-500">Average Property Price</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {analytics.investment_trends.avg_roi_trend.toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-500">Average ROI</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {analytics.investment_trends.high_priority_percentage.toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-500">High Priority Rate</div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-500">No analytics data available</div>
          </div>
        )}
      </main>
    </div>
  );
}
