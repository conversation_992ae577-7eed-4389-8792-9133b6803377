'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import LoadingSpinner from '@/components/LoadingSpinner';

interface SearchCriteria {
  name: string;
  state: string;
  city?: string;
  min_price: number;
  max_price: number;
  min_bedrooms: number;
  max_bedrooms: number;
  min_bathrooms: number;
  property_types: string[];
  min_roi: number;
  search_radius: number;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
}

export default function CreateSearchPage() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  const [criteria, setCriteria] = useState<SearchCriteria>({
    name: '',
    state: '',
    city: '',
    min_price: 50000,
    max_price: 200000,
    min_bedrooms: 2,
    max_bedrooms: 4,
    min_bathrooms: 1,
    property_types: ['SFR', 'MFR'],
    min_roi: 10,
    search_radius: 75,
    priority: 'MEDIUM',
  });

  const states = [
    'MI', 'OH', 'PA', 'NY', 'AL', 'TN', 'KY', 'IN', 'NC', 'SC', 
    'GA', 'FL', 'AR', 'MS', 'LA', 'WV', 'IL', 'WI'
  ];

  const propertyTypes = [
    { value: 'SFR', label: 'Single Family Residential' },
    { value: 'MFR', label: 'Multi Family Residential' },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('/api/search/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(criteria),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Search created successfully! It will begin monitoring immediately.');
        // Reset form
        setCriteria({
          name: '',
          state: '',
          city: '',
          min_price: 50000,
          max_price: 200000,
          min_bedrooms: 2,
          max_bedrooms: 4,
          min_bathrooms: 1,
          property_types: ['SFR', 'MFR'],
          min_roi: 10,
          search_radius: 75,
          priority: 'MEDIUM',
        });
      } else {
        throw new Error(data.error || 'Failed to create search');
      }

    } catch (err) {
      console.error('Error creating search:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof SearchCriteria, value: any) => {
    setCriteria(prev => ({ ...prev, [field]: value }));
  };

  const handlePropertyTypeChange = (type: string, checked: boolean) => {
    setCriteria(prev => ({
      ...prev,
      property_types: checked
        ? [...prev.property_types, type]
        : prev.property_types.filter(t => t !== type)
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Create New Search</h1>
          <p className="text-gray-600 mt-2">
            Set up automated monitoring for Section 8 investment opportunities
          </p>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="text-red-800">
              <strong>Error:</strong> {error}
            </div>
          </div>
        )}

        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="text-green-800">
              <strong>Success:</strong> {success}
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Search Name */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search Name *
              </label>
              <input
                type="text"
                required
                value={criteria.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="e.g. Michigan High ROI Properties"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* State */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                State *
              </label>
              <select
                required
                value={criteria.state}
                onChange={(e) => handleInputChange('state', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select State</option>
                {states.map(state => (
                  <option key={state} value={state}>{state}</option>
                ))}
              </select>
            </div>

            {/* City (Optional) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                City (Optional)
              </label>
              <input
                type="text"
                value={criteria.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                placeholder="Leave blank for state-wide search"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Price Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Min Price
              </label>
              <input
                type="number"
                value={criteria.min_price}
                onChange={(e) => handleInputChange('min_price', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Price
              </label>
              <input
                type="number"
                value={criteria.max_price}
                onChange={(e) => handleInputChange('max_price', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Bedrooms */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Min Bedrooms
              </label>
              <select
                value={criteria.min_bedrooms}
                onChange={(e) => handleInputChange('min_bedrooms', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {[1, 2, 3, 4, 5].map(num => (
                  <option key={num} value={num}>{num}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Bedrooms
              </label>
              <select
                value={criteria.max_bedrooms}
                onChange={(e) => handleInputChange('max_bedrooms', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {[2, 3, 4, 5, 6, 7, 8].map(num => (
                  <option key={num} value={num}>{num}</option>
                ))}
              </select>
            </div>

            {/* Property Types */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Property Types
              </label>
              <div className="space-y-2">
                {propertyTypes.map(type => (
                  <label key={type.value} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={criteria.property_types.includes(type.value)}
                      onChange={(e) => handlePropertyTypeChange(type.value, e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">{type.label}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* ROI and Radius */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Min ROI (%)
              </label>
              <input
                type="number"
                value={criteria.min_roi}
                onChange={(e) => handleInputChange('min_roi', parseFloat(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search Radius (miles)
              </label>
              <input
                type="number"
                value={criteria.search_radius}
                onChange={(e) => handleInputChange('search_radius', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Priority */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search Priority
              </label>
              <select
                value={criteria.priority}
                onChange={(e) => handleInputChange('priority', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="HIGH">High Priority</option>
                <option value="MEDIUM">Medium Priority</option>
                <option value="LOW">Low Priority</option>
              </select>
            </div>
          </div>

          {/* Submit Button */}
          <div className="mt-8 flex items-center justify-end space-x-4">
            <button
              type="button"
              onClick={() => window.history.back()}
              className="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {loading && <LoadingSpinner size="small" className="mr-2" />}
              Create Search
            </button>
          </div>
        </form>
      </main>
    </div>
  );
}
