'use client';

import { formatRelativeTime } from '@/lib/utils';

interface HeaderProps {
  onRefresh: () => void;
  refreshing: boolean;
  lastUpdate?: string;
}

export default function Header({ onRefresh, refreshing, lastUpdate }: HeaderProps) {
  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <h1 className="text-2xl font-bold text-gray-900">
                🏠 Section 8 Property Monitor
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                Real-time investment opportunity tracking
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Status Indicator */}
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600">
                {lastUpdate ? `Updated ${formatRelativeTime(lastUpdate)}` : 'Live'}
              </span>
            </div>
            
            {/* Refresh Button */}
            <button
              onClick={onRefresh}
              disabled={refreshing}
              className={`
                flex items-center px-4 py-2 text-sm font-medium rounded-lg border transition-colors
                ${refreshing 
                  ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed' 
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:text-gray-900'
                }
              `}
            >
              <svg
                className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </button>
            
            {/* Navigation Menu */}
            <nav className="hidden md:flex space-x-4">
              <a
                href="/"
                className="text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium"
              >
                Dashboard
              </a>
              <a
                href="/properties"
                className="text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium"
              >
                Properties
              </a>
              <a
                href="/markets"
                className="text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium"
              >
                Markets
              </a>
              <a
                href="/analytics"
                className="text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium"
              >
                Analytics
              </a>
            </nav>
            
            {/* Mobile Menu Button */}
            <button className="md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}
