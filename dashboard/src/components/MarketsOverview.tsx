'use client';

import { MarketData } from '@/types';
import { formatPercentage, formatRelativeTime } from '@/lib/utils';

interface MarketsOverviewProps {
  markets: MarketData[];
}

export default function MarketsOverview({ markets }: MarketsOverviewProps) {
  const topMarkets = markets.slice(0, 8); // Show top 8 markets

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Top Markets</h3>
        <p className="text-sm text-gray-600">Performance over the last 7 days</p>
      </div>
      
      <div className="p-6">
        {topMarkets.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 text-4xl mb-3">🗺️</div>
            <p className="text-gray-600">No market data available</p>
          </div>
        ) : (
          <div className="space-y-4">
            {topMarkets.map((market, index) => (
              <div
                key={`${market.city}-${market.state}`}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        {market.city}, {market.state}
                      </h4>
                      <p className="text-xs text-gray-500">
                        Last scanned {formatRelativeTime(market.last_scanned)}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="flex-shrink-0 text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {market.properties_found} properties
                  </div>
                  <div className="flex items-center space-x-2 text-xs">
                    <span className="text-red-600">
                      {market.high_priority_count} high
                    </span>
                    <span className="text-yellow-600">
                      {market.medium_priority_count} med
                    </span>
                  </div>
                  <div className="text-xs text-gray-500">
                    {formatPercentage(market.avg_roi)} avg ROI
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
        
        {markets.length > 8 && (
          <div className="mt-4 text-center">
            <a
              href="/markets"
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              View all markets →
            </a>
          </div>
        )}
      </div>
    </div>
  );
}
