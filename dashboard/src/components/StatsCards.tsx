'use client';

import { DashboardStats } from '@/types';
import { formatCurrency, formatNumber, formatPercentage } from '@/lib/utils';

interface StatsCardsProps {
  stats: DashboardStats;
}

export default function StatsCards({ stats }: StatsCardsProps) {
  const cards = [
    {
      title: 'Total Properties',
      value: formatNumber(stats.total_properties),
      change: `+${stats.today_found} today`,
      changeType: stats.today_found > 0 ? 'positive' : 'neutral',
      icon: '🏠',
      color: 'blue',
    },
    {
      title: 'High Priority',
      value: formatNumber(stats.high_priority),
      change: `${formatPercentage((stats.high_priority / stats.total_properties) * 100, 0)} of total`,
      changeType: 'neutral',
      icon: '🔥',
      color: 'red',
    },
    {
      title: 'Average ROI',
      value: formatPercentage(stats.avg_roi),
      change: 'Target: 15%+',
      changeType: stats.avg_roi >= 15 ? 'positive' : stats.avg_roi >= 10 ? 'neutral' : 'negative',
      icon: '📈',
      color: 'green',
    },
    {
      title: 'Avg Cash Flow',
      value: formatCurrency(stats.avg_cash_flow),
      change: 'Target: $200+',
      changeType: stats.avg_cash_flow >= 200 ? 'positive' : stats.avg_cash_flow >= 100 ? 'neutral' : 'negative',
      icon: '💰',
      color: 'purple',
    },
    {
      title: 'Markets Monitored',
      value: formatNumber(stats.markets_monitored),
      change: 'Active scanning',
      changeType: 'positive',
      icon: '🗺️',
      color: 'indigo',
    },
    {
      title: 'Medium Priority',
      value: formatNumber(stats.medium_priority),
      change: `${formatPercentage((stats.medium_priority / stats.total_properties) * 100, 0)} of total`,
      changeType: 'neutral',
      icon: '⚡',
      color: 'yellow',
    },
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-50 text-blue-600',
      red: 'bg-red-50 text-red-600',
      green: 'bg-green-50 text-green-600',
      purple: 'bg-purple-50 text-purple-600',
      indigo: 'bg-indigo-50 text-indigo-600',
      yellow: 'bg-yellow-50 text-yellow-600',
    };
    return colorMap[color as keyof typeof colorMap] || 'bg-gray-50 text-gray-600';
  };

  const getChangeColorClasses = (changeType: string) => {
    switch (changeType) {
      case 'positive':
        return 'text-green-600';
      case 'negative':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
      {cards.map((card, index) => (
        <div
          key={index}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 card-hover"
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600 mb-1">
                {card.title}
              </p>
              <p className="text-2xl font-bold text-gray-900 mb-1">
                {card.value}
              </p>
              <p className={`text-xs ${getChangeColorClasses(card.changeType)}`}>
                {card.change}
              </p>
            </div>
            <div className={`w-12 h-12 rounded-lg flex items-center justify-center text-xl ${getColorClasses(card.color)}`}>
              {card.icon}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
