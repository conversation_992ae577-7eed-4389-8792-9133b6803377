export interface Property {
  id: number;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  estimated_value: number;
  bedrooms: number;
  bathrooms: number;
  square_feet: number;
  lot_size: number;
  year_built: number;
  property_type: string;
  days_on_market: number;
  estimated_rent: number;
  investment_score: number;
  investment_priority: 'HIGH' | 'MEDIUM' | 'LOW';
  roi_percentage: number;
  cash_flow: number;
  fmr_2br: number;
  fmr_3br: number;
  discovered_at: string;
}

export interface MonitoringStats {
  date: string;
  properties_analyzed: number;
  high_priority_found: number;
  alerts_sent: number;
  api_calls_made: number;
  markets_processed: number;
  errors_count: number;
}

export interface EmailAlert {
  id: number;
  property_id: number;
  alert_type: string;
  sent_at: string;
  email_status: string;
  error_message?: string;
}

export interface DashboardStats {
  total_properties: number;
  high_priority: number;
  medium_priority: number;
  low_priority: number;
  today_found: number;
  avg_roi: number;
  avg_cash_flow: number;
  markets_monitored: number;
  last_update: string;
}

export interface FilterOptions {
  priority?: 'HIGH' | 'MEDIUM' | 'LOW' | 'ALL';
  city?: string;
  state?: string;
  min_roi?: number;
  max_price?: number;
  min_cash_flow?: number;
  days_back?: number;
  sort_by?: 'investment_score' | 'roi_percentage' | 'cash_flow' | 'discovered_at';
  sort_order?: 'asc' | 'desc';
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface SystemStatus {
  database_connected: boolean;
  api_status: {
    real_estate_api: boolean;
    hud_api: boolean;
    email_service: boolean;
  };
  monitoring_active: boolean;
  last_scan: string;
  next_scan: string;
  errors_count: number;
}

export interface MarketData {
  city: string;
  state: string;
  priority: string;
  properties_found: number;
  high_priority_count: number;
  medium_priority_count: number;
  avg_roi: number;
  last_scanned: string;
}

export interface SystemStatus {
  database_connected: boolean;
  api_status: {
    real_estate_api: boolean;
    hud_api: boolean;
    email_service: boolean;
  };
  monitoring_active: boolean;
  last_scan: string;
  next_scan: string;
  errors_count: number;
}
