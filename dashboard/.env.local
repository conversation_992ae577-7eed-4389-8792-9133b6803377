# Next.js Dashboard Environment Variables
# Copy this to .env.local and update with your values

# Database Connection (same as main application)
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Application Settings
NEXT_PUBLIC_APP_NAME="Section 8 Property Monitor"
NEXT_PUBLIC_APP_VERSION="4.0.0"

# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api

# Development Settings
NODE_ENV=development
