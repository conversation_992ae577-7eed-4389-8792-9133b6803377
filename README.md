# Section 8 Property Monitor - Production Application

A comprehensive real-time property monitoring system designed to identify Section 8 investment opportunities across 15-20 top cities. The system runs on 1-minute intervals, stores data in PostgreSQL, and sends email alerts for high-priority properties.

## 🏗️ Architecture

### Core Components
- **Production Monitor** (`production_monitor.py`) - Main monitoring application
- **Next.js Dashboard** (`dashboard/`) - Modern web interface
- **PostgreSQL Database** - Neon cloud database for data storage
- **Email Notifications** - Resend API for instant alerts
- **PythonAnywhere Deployment** - Cloud hosting platform

### Key Features
- ⏰ **1-minute interval monitoring** of 20 top Section 8 markets
- 🎯 **Focus on new listings** (days on market < 1)
- 📊 **Advanced investment scoring** algorithm
- 📧 **Instant email alerts** for high-priority properties
- 🗄️ **PostgreSQL database** with comprehensive property data
- 📱 **Responsive Next.js dashboard** with real-time updates
- 🔒 **Secure API handling** with server-side data fetching

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Clone the repository
git clone <repository-url>
cd section8-monitor-final

# Install Python dependencies
pip install -r requirements.txt

# Install Node.js dependencies for dashboard
cd dashboard
npm install
cd ..
```

### 2. Environment Variables
Create a `.env` file with the following variables:
```env
# Database (Neon PostgreSQL)
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Email Notifications (Resend)
RESEND_API_KEY=re_Yp76vKMM_8W3kEDTidZSCJ77Uc3yiyZgP
ALERT_EMAIL=<EMAIL>

# Real Estate API
REAL_ESTATE_API_KEY=AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914
REAL_ESTATE_API_URL=https://api.realestateapi.com

# HUD API
HUD_API_KEY=<your-hud-api-key>
HUD_API_URL=https://www.huduser.gov/hudapi/public
```

### 3. Local Development

#### Automated Development Environment (Recommended)
```bash
# Start both backend and dashboard automatically
python start_development.py
```

This will:
- Install all dependencies automatically
- Start the real-time monitoring backend
- Start the Next.js dashboard
- Set up 1-minute interval monitoring
- Open dashboard at http://localhost:3000

#### Manual Development Setup
```bash
# Run the real-time monitor
python realtime_section8_monitor.py

# Run the Next.js dashboard (in another terminal)
cd dashboard
npm run dev
```

## 🌐 Production Deployment

### PythonAnywhere Deployment
```bash
# Run the deployment setup script
python deploy_pythonanywhere.py

# Follow the generated instructions in DEPLOYMENT_INSTRUCTIONS.md
```

### Manual Deployment Steps
1. **Upload Files** to PythonAnywhere
2. **Set up Scheduled Task** to run every 1 minute
3. **Configure Web App** for the dashboard
4. **Test the Setup** using the provided test script

## 📊 Dashboard Features

### Main Dashboard
- **Real-time statistics** - Total properties, high priority count, average ROI
- **Countdown timer** - Shows time until next scan (1-minute intervals)
- **Collapsible city preview** - 5 cities + "view more" expansion
- **Recent properties table** - Latest discoveries with investment analysis
- **Market performance** - Top performing cities and regions
- **Quick actions** - Search, create search, analytics, and settings

### Properties Page
- **Advanced filtering** - By priority, location, ROI, cash flow
- **Sorting options** - Score, ROI, discovery date, price
- **Individual property pages** - Detailed view with Zillow integration
- **Enhanced property data** - Zestimate, rent estimates, neighborhood info
- **Interactive elements** - All buttons and forms fully functional

### Search Creation
- **Multiple search capability** - Create custom searches for specific markets
- **Editable parameters** - Price range, bedrooms, property types, ROI targets
- **State-level searches** - Broader geographic coverage with increased radius
- **Auto-parameter adjustment** - Automatically adjusts when no properties found

### Analytics Dashboard
- **Performance insights** - Daily stats, market trends, investment analysis
- **Market performance** - State-by-state breakdown with top cities
- **Investment trends** - ROI trends, price analysis, priority rates

### Markets Page
- **Market performance metrics** - Properties found, success rates
- **Geographic distribution** - City and state breakdown
- **Trend analysis** - Historical performance data

## 🎯 Target Markets

The system monitors 20 top Section 8 investment markets:

### Tier 1 - Rust Belt Opportunities
- Detroit, MI
- Cleveland, OH  
- Pittsburgh, PA
- Buffalo, NY
- Milwaukee, WI

### Tier 2 - Midwest Value
- Akron, OH
- Toledo, OH
- Youngstown, OH
- Dayton, OH
- Flint, MI

### Tier 3 - Southern Opportunities
- Birmingham, AL
- Memphis, TN
- Jackson, MS
- New Bern, NC
- Wilmington, NC

### Tier 4 - Emerging Markets
- Fort Wayne, IN
- Evansville, IN
- Springfield, IL
- Rockford, IL
- Peoria, IL

## 📈 Investment Scoring Algorithm

### Scoring Components (0-100 scale)
- **ROI Score (40%)** - Return on investment percentage
- **Cash Flow Score (30%)** - Monthly cash flow potential
- **Price Score (20%)** - Property value vs. market
- **Market Timing Score (10%)** - Days on market (newer = better)

### Priority Levels
- **HIGH (80-100)** - Immediate email alerts sent
- **MEDIUM (60-79)** - Tracked and reported
- **LOW (<60)** - Filtered out

### Investment Criteria
- **Minimum ROI**: 10%
- **Preferred ROI**: 15%+
- **Minimum Cash Flow**: $100/month
- **Maximum Days on Market**: 1 day
- **Property Types**: Single family, multi-family, townhouse, duplex

## 📧 Email Notifications

### Alert Types
- **High Priority Property Alerts** - Instant notifications for score 80+
- **Daily Summary Reports** - End-of-day statistics and top finds
- **Weekly Market Reports** - Market performance analysis
- **System Status Alerts** - Error notifications and health checks

### Email Content
- Property details and photos
- Investment analysis and projections
- Market context and comparisons
- Direct links to property listings

## 🗄️ Database Schema

### Tables
- **property_leads** - Main property data and analysis
- **monitoring_stats** - Daily performance statistics
- **email_alerts** - Email notification tracking
- **market_performance** - Historical market data

### Key Indexes
- Investment priority and score
- Discovery date and location
- ROI and cash flow ranges

## 🔧 Configuration

### System Settings (`config.json`)
- Search interval: 1 minute
- Concurrent searches: 15
- API rate limiting: 2 seconds
- Max properties per search: 50

### Investment Filters
- Price ranges by market tier
- Property specifications (bedrooms, bathrooms, sq ft)
- Financial targets (ROI, cash flow, cap rate)
- Property conditions and ownership status

## 📱 API Endpoints

### Dashboard API
- `GET /api/stats` - Dashboard statistics
- `GET /api/properties` - Property listings with filtering
- `GET /api/markets` - Market performance data
- `GET /api/alerts` - Email alert history

### Monitoring API
- `POST /api/scan` - Trigger manual scan
- `GET /api/status` - System health check
- `POST /api/test-email` - Send test notification

## 🔍 Monitoring & Logging

### Performance Tracking
- Properties analyzed per day
- High priority discoveries
- API call usage and limits
- Error rates and recovery

### Log Files
- Daily rotating logs
- Error tracking and alerts
- Performance metrics
- Database query optimization

## 🛠️ Troubleshooting

### Common Issues
1. **Database Connection** - Check connection string and SSL settings
2. **API Rate Limits** - Adjust delay settings in configuration
3. **Email Delivery** - Verify Resend API key and domain setup
4. **Memory Usage** - Monitor PythonAnywhere CPU seconds

### Debug Mode
```bash
# Run with verbose logging
python production_monitor.py --debug

# Test database connection
python test_setup.py

# Verify email configuration
python -c "import resend; print('Email service ready')"
```

## 📞 Support

For issues or questions:
- Check the troubleshooting section
- Review log files for error details
- Verify all environment variables are set
- Test individual components using provided scripts

## 🔄 Updates and Maintenance

### Regular Tasks
- Monitor API usage and limits
- Review and adjust investment criteria
- Update target market lists
- Optimize database queries
- Check email deliverability

### Scaling Considerations
- Increase PythonAnywhere plan for more CPU seconds
- Implement caching for frequently accessed data
- Add more concurrent processing
- Expand to additional markets

---

**Built for serious Section 8 investors who need real-time market intelligence and automated opportunity discovery.**
