#!/usr/bin/env python3
"""
Section 8 Investor Pro - Main Application
Advanced nationwide Section 8 property monitoring system
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

from src.core.config import Config
from src.core.database import DatabaseManager
from src.core.scheduler import PropertyMonitorScheduler
from src.api.real_estate_client import RealEstateAPIClient
from src.api.hud_client import HUDAPIClient
from src.services.property_analyzer import PropertyAnalyzer
from src.services.market_scanner import MarketScanner
from src.services.notification_service import NotificationService
from src.utils.logger import setup_logging

class Section8InvestorPro:
    """Main application class for Section 8 property monitoring"""
    
    def __init__(self):
        """Initialize the application"""
        self.config = Config()
        self.logger = setup_logging(self.config.LOG_LEVEL)
        self.db_manager = DatabaseManager(self.config.DATABASE_URL)
        
        # Initialize API clients
        self.real_estate_client = RealEstateAPIClient(
            api_key=self.config.REAL_ESTATE_API_KEY,
            base_url=self.config.REAL_ESTATE_API_URL,
            rate_limit_delay=self.config.API_RATE_LIMIT_DELAY
        )
        
        self.hud_client = HUDAPIClient(
            api_key=self.config.HUD_API_KEY,
            base_url=self.config.HUD_API_URL
        )
        
        # Initialize services
        self.property_analyzer = PropertyAnalyzer(
            real_estate_client=self.real_estate_client,
            hud_client=self.hud_client,
            config=self.config
        )
        
        self.market_scanner = MarketScanner(
            real_estate_client=self.real_estate_client,
            property_analyzer=self.property_analyzer,
            config=self.config
        )
        
        self.notification_service = NotificationService(
            config=self.config
        )
        
        # Initialize scheduler
        self.scheduler = PropertyMonitorScheduler(
            market_scanner=self.market_scanner,
            db_manager=self.db_manager,
            notification_service=self.notification_service,
            config=self.config
        )
        
        self.logger.info("Section 8 Investor Pro initialized successfully")
    
    async def start_monitoring(self):
        """Start the property monitoring system"""
        self.logger.info("Starting Section 8 property monitoring system...")
        
        try:
            # Initialize database
            await self.db_manager.initialize()
            self.logger.info("Database initialized")
            
            # Test API connections
            await self._test_api_connections()
            
            # Start the scheduler
            await self.scheduler.start()
            
        except Exception as e:
            self.logger.error(f"Failed to start monitoring system: {str(e)}")
            raise
    
    async def _test_api_connections(self):
        """Test all API connections"""
        self.logger.info("Testing API connections...")
        
        # Test Real Estate API
        try:
            test_result = await self.real_estate_client.test_connection()
            if test_result:
                self.logger.info("✅ Real Estate API connection successful")
            else:
                raise Exception("Real Estate API connection failed")
        except Exception as e:
            self.logger.error(f"❌ Real Estate API connection failed: {str(e)}")
            raise
        
        # Test HUD API
        try:
            test_result = await self.hud_client.test_connection()
            if test_result:
                self.logger.info("✅ HUD API connection successful")
            else:
                self.logger.warning("⚠️ HUD API connection failed, will use fallback FMR data")
        except Exception as e:
            self.logger.warning(f"⚠️ HUD API connection failed: {str(e)}, will use fallback FMR data")
    
    async def stop_monitoring(self):
        """Stop the monitoring system gracefully"""
        self.logger.info("Stopping Section 8 property monitoring system...")
        
        try:
            await self.scheduler.stop()
            await self.db_manager.close()
            self.logger.info("System stopped successfully")
        except Exception as e:
            self.logger.error(f"Error stopping system: {str(e)}")
    
    async def run_single_scan(self, markets=None):
        """Run a single scan for testing purposes"""
        self.logger.info("Running single property scan...")
        
        try:
            await self.db_manager.initialize()
            
            if markets is None:
                # Use first 3 high priority markets for testing
                markets = self.config.get_tier_1_markets()[:3]
            
            results = await self.market_scanner.scan_markets(markets)
            
            self.logger.info(f"Single scan completed: {results['total_properties']} properties analyzed")
            return results
            
        except Exception as e:
            self.logger.error(f"Single scan failed: {str(e)}")
            raise

async def main():
    """Main entry point"""
    app = Section8InvestorPro()
    
    try:
        if len(sys.argv) > 1 and sys.argv[1] == "--test":
            # Run single scan for testing
            results = await app.run_single_scan()
            print(f"Test scan completed: {results}")
        else:
            # Start continuous monitoring
            await app.start_monitoring()
            
            # Keep running until interrupted
            try:
                while True:
                    await asyncio.sleep(60)
            except KeyboardInterrupt:
                print("\nReceived interrupt signal, stopping...")
                await app.stop_monitoring()
                
    except Exception as e:
        logging.error(f"Application error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
