#!/usr/bin/env python3
"""
Quick Start Script for Section 8 Property Monitor
Run this to test the system locally before deployment
"""

import asyncio
import sys
import os
from datetime import datetime

async def run_single_cycle():
    """Run a single monitoring cycle for testing"""
    print("🚀 Starting Section 8 Property Monitor - Test Run")
    print("=" * 60)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Import the production monitor
        from production_monitor import Section8ProductionMonitor
        
        # Create and initialize monitor
        monitor = Section8ProductionMonitor()
        await monitor.initialize()
        
        print("✅ Monitor initialized successfully")
        print(f"📍 Monitoring {len(monitor.target_cities)} cities")
        print(f"📧 Email alerts: {monitor.alert_email}")
        print(f"🗄️ Database: Connected")
        print()
        
        # Run a single monitoring cycle
        print("🔍 Running monitoring cycle...")
        await monitor.run_monitoring_cycle()
        
        # Show results
        stats = monitor.daily_stats
        print("\n📊 Cycle Results:")
        print(f"  Properties Analyzed: {stats['properties_analyzed']}")
        print(f"  High Priority Found: {stats['high_priority_found']}")
        print(f"  Alerts Sent: {stats['alerts_sent']}")
        print(f"  Markets Processed: {stats['markets_processed']}")
        print(f"  API Calls Made: {stats['api_calls_made']}")
        print(f"  Errors: {stats['errors_count']}")
        
        # Cleanup
        await monitor.cleanup()
        
        print("\n✅ Test run completed successfully!")
        print("\n🎯 Next Steps:")
        print("1. Review any properties found in the database")
        print("2. Check your email for any alerts sent")
        print("3. Deploy to PythonAnywhere for continuous monitoring")
        print("4. Access the dashboard to view results")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error during test run: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Run: python test_production_system.py")
        print("2. Check your .env file has all required variables")
        print("3. Verify database connection")
        print("4. Confirm API keys are valid")
        return False

def main():
    """Main function"""
    print("Section 8 Property Monitor - Quick Start Test")
    print()
    
    # Check if this is the first run
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("Please create a .env file with your configuration.")
        print("See README.md for required environment variables.")
        return
    
    # Run the test
    try:
        success = asyncio.run(run_single_cycle())
        if success:
            print("\n🎉 System is working correctly!")
            print("Ready for production deployment.")
        else:
            print("\n⚠️ Issues found. Please fix before deployment.")
            
    except KeyboardInterrupt:
        print("\n\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
