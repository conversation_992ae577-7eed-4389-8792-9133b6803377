
import sys
import os
from pathlib import Path

# Add your project directory to the Python path
project_home = '/home/<USER>/section8-monitor'  # Update this path
if project_home not in sys.path:
    sys.path.insert(0, project_home)

# Set environment variables
os.environ['DATABASE_URL'] = 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require'
os.environ['RESEND_API_KEY'] = 're_Yp76vKMM_8W3kEDTidZSCJ77Uc3yiyZgP'
os.environ['ALERT_EMAIL'] = '<EMAIL>'

# Import your Flask/FastAPI app
from src.web.dashboard import app as application

# For debugging
if __name__ == "__main__":
    application.run(debug=True)
