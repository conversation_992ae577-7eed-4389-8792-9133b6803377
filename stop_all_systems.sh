#!/bin/bash

# Section 8 Property Monitor - System Shutdown Script
# This script stops all running components gracefully

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Stop process by PID file
stop_process() {
    local service_name=$1
    local pid_file=$2
    
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            print_status "Stopping $service_name (PID: $pid)..."
            kill $pid
            sleep 2
            
            # Force kill if still running
            if ps -p $pid > /dev/null 2>&1; then
                print_warning "Force killing $service_name..."
                kill -9 $pid
            fi
            
            rm -f "$pid_file"
            print_status "$service_name stopped"
        else
            print_warning "$service_name was not running"
            rm -f "$pid_file"
        fi
    else
        print_warning "No PID file found for $service_name"
    fi
}

# Stop Python monitoring systems
stop_python_monitors() {
    print_header "STOPPING PYTHON MONITORING SYSTEMS"
    
    stop_process "Production Monitor" "logs/production_monitor.pid"
    stop_process "Main Application" "logs/main_application.pid"
}

# Stop Node.js dashboards
stop_dashboards() {
    print_header "STOPPING DASHBOARDS"
    
    stop_process "Next.js Dashboard" "logs/nextjs_dashboard.pid"
    stop_process "Live Dashboard" "logs/live_dashboard.pid"
}

# Stop Docker Compose stack
stop_docker_stack() {
    print_header "STOPPING DOCKER COMPOSE STACK"
    
    print_status "Stopping Docker services..."
    
    # Use sudo if available, otherwise try without
    if command -v sudo &> /dev/null && [[ $EUID -ne 0 ]]; then
        sudo docker-compose down
    else
        docker-compose down
    fi
    
    print_status "Docker stack stopped"
}

# Kill any remaining processes
cleanup_remaining_processes() {
    print_header "CLEANING UP REMAINING PROCESSES"
    
    # Kill any remaining Node.js processes on ports 3000 and 3001
    print_status "Checking for processes on ports 3000 and 3001..."
    
    for port in 3000 3001; do
        local pid=$(lsof -ti:$port 2>/dev/null || true)
        if [[ -n "$pid" ]]; then
            print_status "Killing process on port $port (PID: $pid)"
            kill $pid 2>/dev/null || true
        fi
    done
    
    # Kill any remaining Python monitoring processes
    print_status "Checking for remaining Python monitoring processes..."
    pkill -f "production_monitor.py" 2>/dev/null || true
    pkill -f "main.py" 2>/dev/null || true
    
    print_status "Cleanup complete"
}

# Display final status
show_final_status() {
    print_header "SHUTDOWN COMPLETE"
    
    echo -e "${GREEN}✅ All systems stopped successfully!${NC}"
    echo ""
    echo -e "${BLUE}Stopped Services:${NC}"
    echo -e "  ${GREEN}• Production Monitor${NC}"
    echo -e "  ${GREEN}• Main Application${NC}"
    echo -e "  ${GREEN}• Next.js Dashboard${NC}"
    echo -e "  ${GREEN}• Live Dashboard${NC}"
    echo -e "  ${GREEN}• Docker Stack (PostgreSQL, Redis, Nginx, Prometheus, Grafana)${NC}"
    echo ""
    echo -e "${YELLOW}💡 Use 'start_all_systems.sh' to restart all services${NC}"
}

# Main execution
main() {
    print_header "SECTION 8 PROPERTY MONITOR - SYSTEM SHUTDOWN"
    
    stop_dashboards
    stop_python_monitors
    stop_docker_stack
    cleanup_remaining_processes
    
    show_final_status
}

# Run main function
main "$@"
