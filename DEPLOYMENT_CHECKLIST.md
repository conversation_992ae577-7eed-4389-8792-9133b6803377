# Section 8 Property Monitor - Deployment Checklist

## ✅ Pre-Deployment Verification

### System Tests
- [x] **All tests passing** - `python test_production_system.py` ✅
- [x] **Environment variables configured** - Database, Email, API keys ✅
- [x] **Database connection verified** - Neon PostgreSQL ✅
- [x] **Email service configured** - Resend API ✅
- [x] **Dependencies installed** - All Python packages ✅
- [x] **Configuration validated** - config.json settings ✅

### Core Files Ready
- [x] `production_monitor.py` - Main monitoring application
- [x] `config.json` - System configuration
- [x] `.env` - Environment variables
- [x] `requirements_pythonanywhere.txt` - Dependencies
- [x] `run_monitor.py` - Task execution script
- [x] `wsgi.py` - Web app configuration

### Dashboard Files Ready
- [x] `dashboard/` - Complete Next.js application
- [x] `dashboard/package.json` - Dependencies configured
- [x] `dashboard/src/app/` - Main application pages
- [x] `dashboard/src/components/` - UI components
- [x] `dashboard/src/lib/` - Utilities and database
- [x] `dashboard/.env.local` - Dashboard environment

## 🚀 PythonAnywhere Deployment Steps

### Step 1: File Upload
Upload these essential files to your PythonAnywhere account:

**Core Application:**
```
/home/<USER>/section8-monitor/
├── production_monitor.py
├── config.json
├── .env
├── requirements_pythonanywhere.txt
├── run_monitor.py
├── wsgi.py
├── test_setup.py
└── logs/ (create empty directory)
```

**Dashboard (Optional):**
```
/home/<USER>/section8-monitor/dashboard/
├── package.json
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
├── postcss.config.js
├── .env.local
└── src/ (entire directory)
```

### Step 2: Install Dependencies
```bash
# In PythonAnywhere console
cd /home/<USER>/section8-monitor
pip3.10 install --user -r requirements_pythonanywhere.txt
```

### Step 3: Test Installation
```bash
# Run the test script
python3.10 test_setup.py

# Should show all green checkmarks ✅
```

### Step 4: Create Scheduled Task
1. Go to **PythonAnywhere Dashboard** → **Tasks**
2. Click **"Create a scheduled task"**
3. **Command:** `python3.10 /home/<USER>/section8-monitor/run_monitor.py`
4. **Schedule:** `Every 1 minute`
5. **Description:** `Section 8 Property Monitor`
6. Click **"Create"**
7. **Enable** the task

### Step 5: Set Up Web App (Optional)
1. Go to **PythonAnywhere Dashboard** → **Web**
2. Click **"Add a new web app"**
3. Choose **"Manual configuration"**
4. Select **Python 3.10**
5. **Source code:** `/home/<USER>/section8-monitor`
6. **WSGI configuration file:** `/home/<USER>/section8-monitor/wsgi.py`
7. Click **"Reload"** to start the web app

## 📊 Post-Deployment Verification

### Immediate Checks (First 5 minutes)
- [ ] Task is running without errors
- [ ] Check **Tasks** page for green status
- [ ] No error messages in task logs
- [ ] Database connection successful

### Short-term Checks (First hour)
- [ ] Properties being discovered and saved
- [ ] Email alerts being sent for high-priority properties
- [ ] Database tables being populated
- [ ] API calls within rate limits

### Daily Checks (First week)
- [ ] Daily summary emails received
- [ ] Property count increasing
- [ ] High-priority properties identified
- [ ] System performance stable

## 📧 Expected Email Notifications

### High Priority Alerts
- **Frequency:** As properties are found (could be 5-20 per day)
- **Content:** Property details, investment analysis, action required
- **Trigger:** Properties with score 80+ points

### Daily Summary
- **Frequency:** Once per day (after 6 PM)
- **Content:** Daily statistics, top properties, market performance
- **Trigger:** Automatic at end of day

### System Alerts
- **Frequency:** As needed
- **Content:** Error notifications, system status
- **Trigger:** System failures or issues

## 🔍 Monitoring & Troubleshooting

### Performance Metrics to Watch
- **Properties per day:** 500-1,000 expected
- **High priority finds:** 10-50 per day expected
- **API calls:** 1,440 per day (1 per minute)
- **Error rate:** Should be < 5%

### Common Issues & Solutions

**Task Not Running:**
- Check task is enabled in PythonAnywhere
- Verify file paths are correct
- Check Python version (use 3.10)

**Database Errors:**
- Verify DATABASE_URL in .env file
- Check Neon database is accessible
- Confirm SSL settings

**No Email Alerts:**
- Verify RESEND_API_KEY is correct
- Check ALERT_EMAIL address
- Confirm Resend account is active

**API Rate Limits:**
- Monitor API usage in logs
- Adjust delay settings if needed
- Check API key limits

### Log Files Location
```
/home/<USER>/section8-monitor/logs/
└── section8_monitor_YYYYMMDD.log
```

### Debug Commands
```bash
# Test system manually
python3.10 production_monitor.py

# Check specific components
python3.10 test_setup.py

# View recent logs
tail -f logs/section8_monitor_$(date +%Y%m%d).log
```

## 📈 Success Metrics

### Week 1 Goals
- [ ] System running 24/7 without major errors
- [ ] 50+ properties discovered
- [ ] 5+ high-priority alerts sent
- [ ] All 20 markets being scanned

### Month 1 Goals
- [ ] 1,000+ properties in database
- [ ] 100+ high-priority opportunities identified
- [ ] Investment criteria refined based on results
- [ ] ROI tracking showing profitable opportunities

## 🔐 Security Checklist

### Environment Variables
- [x] DATABASE_URL - Secure PostgreSQL connection
- [x] RESEND_API_KEY - Valid email service key
- [x] REAL_ESTATE_API_KEY - Valid property data key
- [x] ALERT_EMAIL - Correct recipient address

### Access Control
- [x] PythonAnywhere account secured
- [x] Database credentials protected
- [x] API keys not exposed in code
- [x] HTTPS encryption for all communications

## 📞 Support Resources

### Documentation
- `README.md` - Complete system overview
- `PRODUCTION_DEPLOYMENT_SUMMARY.md` - Detailed summary
- `DEPLOYMENT_INSTRUCTIONS.md` - Step-by-step guide

### Test Scripts
- `test_production_system.py` - Comprehensive system test
- `quick_start.py` - Single cycle test run
- `test_setup.py` - PythonAnywhere specific test

### Configuration Files
- `config.json` - System settings and markets
- `.env` - Environment variables
- `requirements_pythonanywhere.txt` - Dependencies

---

## 🎯 Final Deployment Command

Once everything is uploaded and configured:

```bash
# Enable the scheduled task in PythonAnywhere Dashboard
# The system will automatically start monitoring every minute
```

**🎉 Your Section 8 Property Monitor is now live and scanning 20 cities every minute for investment opportunities!**

**📧 Email alerts:** <EMAIL>  
**🗄️ Database:** Neon PostgreSQL  
**⏰ Frequency:** Every 1 minute, 24/7  
**🎯 Focus:** Properties listed within 24 hours
