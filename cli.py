#!/usr/bin/env python3
"""
Section 8 Investor Pro - Command Line Interface
Management and control interface for the property monitoring system
"""

import asyncio
import click
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

from src.core.config import Config
from src.core.database import DatabaseManager
from src.api.real_estate_client import RealEstateAPIClient
from src.api.hud_client import HUDAPIClient
from src.services.market_scanner import MarketScanner
from src.services.property_analyzer import PropertyAnalyzer
from src.services.notification_service import NotificationService
from src.utils.logger import setup_logging

# Global configuration
config = Config()
logger = setup_logging(config.LOG_LEVEL)

@click.group()
@click.version_option(version="2.0.0")
def cli():
    """Section 8 Investor Pro - Advanced Property Monitoring System"""
    pass

@cli.command()
@click.option("--level", default="INFO", help="Logging level")
def test_config(level):
    """Test system configuration and API connections"""
    click.echo("🔧 Testing Section 8 Investor Pro Configuration...")
    
    try:
        # Test configuration
        click.echo(f"✅ Configuration loaded: {len(config.get_all_markets())} markets configured")
        
        # Test API connections
        async def test_apis():
            # Test Real Estate API
            async with RealEstateAPIClient(
                config.REAL_ESTATE_API_KEY,
                config.REAL_ESTATE_API_URL
            ) as re_client:
                re_success = await re_client.test_connection()
                click.echo(f"{'✅' if re_success else '❌'} Real Estate API: {'Connected' if re_success else 'Failed'}")
            
            # Test HUD API
            async with HUDAPIClient(
                config.HUD_API_KEY,
                config.HUD_API_URL
            ) as hud_client:
                hud_success = await hud_client.test_connection()
                click.echo(f"{'✅' if hud_success else '⚠️'} HUD API: {'Connected' if hud_success else 'Failed (will use fallback data)'}")
            
            # Test database
            try:
                db_manager = DatabaseManager(config.DATABASE_URL)
                await db_manager.initialize()
                click.echo("✅ Database: Connected and initialized")
                await db_manager.close()
            except Exception as e:
                click.echo(f"❌ Database: Failed - {str(e)}")
            
            # Test email service
            notification_service = NotificationService(config)
            email_success = await notification_service.test_email_service()
            click.echo(f"{'✅' if email_success else '❌'} Email Service: {'Connected' if email_success else 'Failed'}")
        
        asyncio.run(test_apis())
        click.echo("\n🎉 Configuration test completed!")
        
    except Exception as e:
        click.echo(f"❌ Configuration test failed: {str(e)}")
        sys.exit(1)

@cli.command()
@click.option("--markets", default="5", help="Number of markets to scan")
@click.option("--criteria", default="primary", help="Investment criteria (primary, opportunity, premium)")
def quick_scan(markets, criteria):
    """Run a quick property scan"""
    click.echo(f"🔍 Running quick scan of top {markets} markets...")
    
    async def run_scan():
        try:
            # Initialize services
            re_client = RealEstateAPIClient(config.REAL_ESTATE_API_KEY, config.REAL_ESTATE_API_URL)
            hud_client = HUDAPIClient(config.HUD_API_KEY, config.HUD_API_URL)
            
            property_analyzer = PropertyAnalyzer(re_client, hud_client, config)
            market_scanner = MarketScanner(re_client, property_analyzer, config)
            
            # Run quick scan
            results = await market_scanner.quick_scan(int(markets))
            
            # Display results
            click.echo(f"\n📊 Scan Results:")
            click.echo(f"Markets Scanned: {results.get('markets_scanned', 0)}")
            click.echo(f"Properties Analyzed: {results.get('total_properties', 0)}")
            click.echo(f"High Priority Found: {results.get('high_priority_count', 0)}")
            click.echo(f"Medium Priority Found: {results.get('medium_priority_count', 0)}")
            
            # Show top properties
            high_priority = results.get('high_priority_properties', [])
            if high_priority:
                click.echo(f"\n🎯 Top High Priority Properties:")
                for i, prop in enumerate(high_priority[:5], 1):
                    analysis = prop.get('analysis')
                    property_data = prop.get('property_data', {})
                    
                    click.echo(f"{i}. {analysis.address}")
                    click.echo(f"   Price: ${property_data.get('estimatedValue', 0):,}")
                    click.echo(f"   Score: {analysis.investment_score}/100")
                    click.echo(f"   ROI: {analysis.roi_percentage:.1f}%")
                    click.echo(f"   Market: {prop.get('market', 'Unknown')}")
            
            await re_client.close()
            await hud_client.close()
            
        except Exception as e:
            click.echo(f"❌ Scan failed: {str(e)}")
            sys.exit(1)
    
    asyncio.run(run_scan())

@cli.command()
@click.option("--state", help="State to scan (e.g., MI, OH)")
@click.option("--priority", default="HIGH", help="Priority level (VERY_HIGH, HIGH, MEDIUM, LOW)")
def scan_markets(state, priority):
    """Scan specific markets"""
    click.echo(f"🗺️ Scanning markets...")
    
    async def run_market_scan():
        try:
            # Initialize services
            re_client = RealEstateAPIClient(config.REAL_ESTATE_API_KEY, config.REAL_ESTATE_API_URL)
            hud_client = HUDAPIClient(config.HUD_API_KEY, config.HUD_API_URL)
            
            property_analyzer = PropertyAnalyzer(re_client, hud_client, config)
            market_scanner = MarketScanner(re_client, property_analyzer, config)
            
            # Determine scan type
            if state:
                results = await market_scanner.scan_state_markets(state)
                scan_type = f"state {state}"
            else:
                results = await market_scanner.scan_priority_markets(priority)
                scan_type = f"{priority} priority markets"
            
            click.echo(f"\n📊 {scan_type.title()} Scan Results:")
            click.echo(f"Markets Scanned: {results.get('markets_scanned', 0)}")
            click.echo(f"Properties Analyzed: {results.get('total_properties', 0)}")
            click.echo(f"Opportunities Found: {len(results.get('all_properties', []))}")
            
            # Market performance
            market_summaries = results.get('market_summaries', [])
            if market_summaries:
                click.echo(f"\n🏙️ Market Performance:")
                for market in sorted(market_summaries, key=lambda x: x.get('opportunities_found', 0), reverse=True)[:10]:
                    click.echo(f"  {market.get('city')}, {market.get('state')}: "
                              f"{market.get('opportunities_found', 0)} opportunities")
            
            await re_client.close()
            await hud_client.close()
            
        except Exception as e:
            click.echo(f"❌ Market scan failed: {str(e)}")
            sys.exit(1)
    
    asyncio.run(run_market_scan())

@cli.command()
@click.option("--output", default="properties.json", help="Output file name")
def export_properties(output):
    """Export discovered properties to file"""
    click.echo(f"📤 Exporting properties to {output}...")
    
    async def export_data():
        try:
            db_manager = DatabaseManager(config.DATABASE_URL)
            await db_manager.initialize()
            
            # Get high priority properties
            properties = await db_manager.get_high_priority_properties(limit=100)
            
            # Export to file
            with open(output, 'w') as f:
                json.dump(properties, f, indent=2, default=str)
            
            click.echo(f"✅ Exported {len(properties)} properties to {output}")
            await db_manager.close()
            
        except Exception as e:
            click.echo(f"❌ Export failed: {str(e)}")
            sys.exit(1)
    
    asyncio.run(export_data())

@cli.command()
def show_stats():
    """Show system statistics"""
    click.echo("📊 Section 8 Investor Pro Statistics")
    
    async def get_stats():
        try:
            db_manager = DatabaseManager(config.DATABASE_URL)
            await db_manager.initialize()
            
            stats = await db_manager.get_dashboard_stats()
            
            click.echo(f"\n📈 Property Statistics:")
            click.echo(f"Total Properties: {stats.get('total_properties', 0)}")
            click.echo(f"High Priority: {stats.get('high_priority', 0)}")
            click.echo(f"Medium Priority: {stats.get('medium_priority', 0)}")
            click.echo(f"Average ROI: {stats.get('avg_roi', 0):.1f}%")
            click.echo(f"Found Today: {stats.get('today_found', 0)}")
            
            # Configuration info
            click.echo(f"\n⚙️ Configuration:")
            click.echo(f"Markets Configured: {len(config.get_all_markets())}")
            click.echo(f"High Priority Markets: {len(config.get_high_priority_markets())}")
            click.echo(f"Search Interval: {config.SEARCH_INTERVAL_MINUTES} minutes")
            
            await db_manager.close()
            
        except Exception as e:
            click.echo(f"❌ Failed to get statistics: {str(e)}")
            sys.exit(1)
    
    asyncio.run(get_stats())

@cli.command()
@click.option("--level", default="INFO", help="Log level to show")
@click.option("--lines", default=50, help="Number of lines to show")
def show_logs(level, lines):
    """Show recent log entries"""
    click.echo(f"📋 Recent log entries (Level: {level})...")
    
    try:
        log_file = Path("logs") / f"section8_monitor_{datetime.now().strftime('%Y%m%d')}.log"
        
        if not log_file.exists():
            click.echo("❌ Log file not found")
            return
        
        with open(log_file, 'r') as f:
            log_lines = f.readlines()
        
        # Filter by level and show recent entries
        filtered_lines = [
            line for line in log_lines[-int(lines):]
            if level.upper() in line
        ]
        
        for line in filtered_lines:
            click.echo(line.strip())
            
    except Exception as e:
        click.echo(f"❌ Failed to read logs: {str(e)}")

@cli.command()
@click.option("--subject", required=True, help="Email subject")
@click.option("--message", required=True, help="Email message")
def send_test_email(subject, message):
    """Send test email"""
    click.echo("📧 Sending test email...")
    
    async def send_email():
        try:
            notification_service = NotificationService(config)
            
            success = await notification_service.send_system_alert(
                alert_type="info",
                message=message,
                details={"test": True, "sent_from": "CLI"}
            )
            
            if success:
                click.echo("✅ Test email sent successfully")
            else:
                click.echo("❌ Test email failed")
                
        except Exception as e:
            click.echo(f"❌ Email failed: {str(e)}")
    
    asyncio.run(send_email())

@cli.command()
def list_markets():
    """List all configured markets"""
    click.echo("🗺️ Configured Markets:")
    
    markets_by_priority = {}
    for market in config.get_all_markets():
        if market.priority not in markets_by_priority:
            markets_by_priority[market.priority] = []
        markets_by_priority[market.priority].append(market)
    
    for priority in ["VERY_HIGH", "HIGH", "MEDIUM", "LOW"]:
        if priority in markets_by_priority:
            click.echo(f"\n{priority} Priority:")
            for market in markets_by_priority[priority]:
                click.echo(f"  • {market.city}, {market.state} (Weight: {market.weight})")

@cli.command()
@click.option("--format", "output_format", default="table", help="Output format (table, json)")
def system_status(output_format):
    """Show comprehensive system status"""
    click.echo("🔍 System Status Check...")
    
    async def check_status():
        try:
            # Check APIs
            api_status = {}
            
            # Real Estate API
            async with RealEstateAPIClient(config.REAL_ESTATE_API_KEY, config.REAL_ESTATE_API_URL) as re_client:
                api_status['real_estate_api'] = await re_client.test_connection()
            
            # HUD API
            async with HUDAPIClient(config.HUD_API_KEY, config.HUD_API_URL) as hud_client:
                api_status['hud_api'] = await hud_client.test_connection()
            
            # Database
            try:
                db_manager = DatabaseManager(config.DATABASE_URL)
                await db_manager.initialize()
                api_status['database'] = True
                await db_manager.close()
            except:
                api_status['database'] = False
            
            # Email
            notification_service = NotificationService(config)
            api_status['email_service'] = await notification_service.test_email_service()
            
            if output_format == "json":
                status_data = {
                    "timestamp": datetime.now().isoformat(),
                    "api_status": api_status,
                    "configuration": config.to_dict()
                }
                click.echo(json.dumps(status_data, indent=2))
            else:
                click.echo("\n📊 Service Status:")
                for service, status in api_status.items():
                    status_icon = "✅" if status else "❌"
                    click.echo(f"  {status_icon} {service.replace('_', ' ').title()}: {'Online' if status else 'Offline'}")
                
                click.echo(f"\n⚙️ Configuration:")
                click.echo(f"  Markets: {len(config.get_all_markets())}")
                click.echo(f"  Scan Interval: {config.SEARCH_INTERVAL_MINUTES} minutes")
                click.echo(f"  Email Alerts: {config.ALERT_EMAIL}")
                
        except Exception as e:
            click.echo(f"❌ Status check failed: {str(e)}")
    
    asyncio.run(check_status())

@cli.command()
def start_monitoring():
    """Start the property monitoring system"""
    click.echo("🚀 Starting Section 8 Property Monitoring System...")
    
    try:
        # Import and run the main application
        from main import Section8InvestorPro
        
        async def run_monitor():
            app = Section8InvestorPro()
            await app.start_monitoring()
        
        asyncio.run(run_monitor())
        
    except KeyboardInterrupt:
        click.echo("\n🛑 Monitoring stopped by user")
    except Exception as e:
        click.echo(f"❌ Monitoring failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    cli()
