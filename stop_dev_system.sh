#!/bin/bash

# Section 8 Property Monitor - Development System Stop Script
# This script stops all running components

echo "🛑 Stopping Section 8 Property Monitor (Development Mode)..."
echo "============================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to stop a service by PID file
stop_service() {
    local name=$1
    local pid_file="pids/${name,,}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        echo -e "${BLUE}Stopping $name (PID: $pid)...${NC}"
        
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            sleep 2
            
            # Force kill if still running
            if kill -0 $pid 2>/dev/null; then
                echo -e "${YELLOW}Force killing $name...${NC}"
                kill -9 $pid
            fi
            
            echo -e "${GREEN}✅ $name stopped${NC}"
        else
            echo -e "${YELLOW}⚠️  $name was not running${NC}"
        fi
        
        rm -f "$pid_file"
    else
        echo -e "${YELLOW}⚠️  No PID file found for $name${NC}"
    fi
}

# Function to stop processes by name
stop_by_name() {
    local process_name=$1
    local display_name=$2
    
    echo -e "${BLUE}Stopping $display_name processes...${NC}"
    
    # Find and kill processes
    pids=$(pgrep -f "$process_name" 2>/dev/null)
    
    if [ ! -z "$pids" ]; then
        echo "Found PIDs: $pids"
        for pid in $pids; do
            echo -e "${BLUE}Killing $display_name process (PID: $pid)...${NC}"
            kill $pid 2>/dev/null
        done
        
        sleep 2
        
        # Force kill any remaining processes
        remaining_pids=$(pgrep -f "$process_name" 2>/dev/null)
        if [ ! -z "$remaining_pids" ]; then
            echo -e "${YELLOW}Force killing remaining $display_name processes...${NC}"
            for pid in $remaining_pids; do
                kill -9 $pid 2>/dev/null
            done
        fi
        
        echo -e "${GREEN}✅ $display_name processes stopped${NC}"
    else
        echo -e "${YELLOW}⚠️  No $display_name processes found${NC}"
    fi
}

echo -e "${BLUE}Stopping services by PID files...${NC}"

# Stop services using PID files
stop_service "Monitor"
stop_service "Dashboard"
stop_service "LiveDashboard"
stop_service "Section8Monitor"

echo ""
echo -e "${BLUE}Stopping remaining processes by name...${NC}"

# Stop any remaining Python monitoring processes
stop_by_name "production_monitor.py" "Production Monitor"
stop_by_name "section8_monitor_pro.py" "Section 8 Monitor"
stop_by_name "main.py" "Main Application"

# Stop Node.js dashboard processes
stop_by_name "npm.*dev" "Dashboard"
stop_by_name "npm.*start" "Live Dashboard"
stop_by_name "next-server" "Next.js Server"

echo ""
echo -e "${BLUE}Cleaning up...${NC}"

# Remove PID directory if empty
if [ -d "pids" ] && [ -z "$(ls -A pids)" ]; then
    rmdir pids
    echo -e "${GREEN}✅ Cleaned up PID directory${NC}"
fi

# Show any remaining related processes
echo ""
echo -e "${BLUE}Checking for remaining processes...${NC}"
remaining=$(ps aux | grep -E "(python.*monitor|npm.*dev|npm.*start|next-server)" | grep -v grep)

if [ ! -z "$remaining" ]; then
    echo -e "${YELLOW}⚠️  Some processes may still be running:${NC}"
    echo "$remaining"
    echo ""
    echo -e "${YELLOW}You may need to manually kill these processes:${NC}"
    echo "ps aux | grep -E '(python.*monitor|npm.*dev|npm.*start)' | grep -v grep"
    echo "kill -9 <PID>"
else
    echo -e "${GREEN}✅ No remaining processes found${NC}"
fi

echo ""
echo -e "${GREEN}🎉 System shutdown complete!${NC}"
echo "============================="
echo ""
echo -e "${BLUE}Log files preserved in:${NC}"
echo "📝 logs/ directory"
echo ""
echo -e "${BLUE}To restart the system:${NC}"
echo "🚀 ./start_dev_system.sh"
echo ""
echo -e "${GREEN}Goodbye! 👋${NC}"
