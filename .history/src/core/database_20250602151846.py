#!/usr/bin/env python3
"""
Database Manager for Section 8 Investor Pro
Handles PostgreSQL database operations with connection pooling
"""

import asyncio
import asyncpg
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import json
from dataclasses import asdict

class DatabaseManager:
    """PostgreSQL database manager with connection pooling"""
    
    def __init__(self, database_url: str, min_connections: int = 5, max_connections: int = 20):
        """
        Initialize database manager
        
        Args:
            database_url: PostgreSQL connection URL
            min_connections: Minimum connections in pool
            max_connections: Maximum connections in pool
        """
        self.database_url = database_url
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.pool = None
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self):
        """Initialize database connection pool and create tables"""
        try:
            # Create connection pool
            self.pool = await asyncpg.create_pool(
                self.database_url,
                min_size=self.min_connections,
                max_size=self.max_connections,
                command_timeout=60,
                server_settings={
                    'application_name': 'Section8InvestorPro',
                }
            )
            
            self.logger.info(f"Database connection pool created ({self.min_connections}-{self.max_connections} connections)")
            
            # Create database schema
            await self._create_schema()
            
            self.logger.info("Database initialization completed")
            
        except Exception as e:
            self.logger.error(f"Database initialization failed: {str(e)}")
            raise
    
    async def _create_schema(self):
        """Create database tables if they don't exist"""
        
        # Properties table
        properties_sql = """
        CREATE TABLE IF NOT EXISTS properties (
            id SERIAL PRIMARY KEY,
            property_id VARCHAR(100) UNIQUE,
            address TEXT NOT NULL,
            city VARCHAR(100),
            state VARCHAR(10),
            zip_code VARCHAR(20),
            county VARCHAR(100),
            
            -- Property Details
            bedrooms INTEGER,
            bathrooms DECIMAL(3,1),
            square_feet INTEGER,
            lot_square_feet INTEGER,
            year_built INTEGER,
            property_type VARCHAR(50),
            property_use VARCHAR(50),
            
            -- Financial Data
            estimated_value INTEGER,
            assessed_value INTEGER,
            last_sale_amount INTEGER,
            last_sale_date DATE,
            
            -- Investment Analysis
            estimated_rent INTEGER,
            fair_market_rent INTEGER,
            estimated_cash_flow INTEGER,
            roi_percentage DECIMAL(5,2),
            investment_score INTEGER,
            investment_priority VARCHAR(20),
            
            -- Property Characteristics
            absentee_owner BOOLEAN,
            owner_occupied BOOLEAN,
            vacant BOOLEAN,
            foreclosure BOOLEAN,
            
            -- Geographic Data
            latitude DECIMAL(10,8),
            longitude DECIMAL(11,8),
            
            -- Metadata
            first_discovered TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            data_source VARCHAR(50),
            raw_data JSONB,
            
            -- Indexes
            CONSTRAINT unique_property_address UNIQUE (address, city, state)
        );
        
        CREATE INDEX IF NOT EXISTS idx_properties_city_state ON properties (city, state);
        CREATE INDEX IF NOT EXISTS idx_properties_priority ON properties (investment_priority);
        CREATE INDEX IF NOT EXISTS idx_properties_score ON properties (investment_score);
        CREATE INDEX IF NOT EXISTS idx_properties_roi ON properties (roi_percentage);
        CREATE INDEX IF NOT EXISTS idx_properties_price ON properties (estimated_value);
        CREATE INDEX IF NOT EXISTS idx_properties_bedrooms ON properties (bedrooms);
        CREATE INDEX IF NOT EXISTS idx_properties_discovered ON properties (first_discovered);
        CREATE INDEX IF NOT EXISTS idx_properties_location ON properties (latitude, longitude);
        """
        
        # Markets table
        markets_sql = """
        CREATE TABLE IF NOT EXISTS markets (
            id SERIAL PRIMARY KEY,
            city VARCHAR(100) NOT NULL,
            state VARCHAR(10) NOT NULL,
            metro VARCHAR(200),
            priority VARCHAR(20),
            weight DECIMAL(3,2),
            
            -- Market Statistics
            total_properties_scanned INTEGER DEFAULT 0,
            high_priority_found INTEGER DEFAULT 0,
            medium_priority_found INTEGER DEFAULT 0,
            low_priority_found INTEGER DEFAULT 0,
            
            -- Performance Metrics
            avg_property_score DECIMAL(5,2),
            avg_roi_percentage DECIMAL(5,2),
            avg_property_price INTEGER,
            
            -- Fair Market Rent Data
            fmr_data JSONB,
            fmr_last_updated TIMESTAMP,
            
            -- Metadata
            last_scanned TIMESTAMP,
            scan_count INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            CONSTRAINT unique_market UNIQUE (city, state)
        );
        
        CREATE INDEX IF NOT EXISTS idx_markets_priority ON markets (priority);
        CREATE INDEX IF NOT EXISTS idx_markets_state ON markets (state);
        CREATE INDEX IF NOT EXISTS idx_markets_last_scanned ON markets (last_scanned);
        """
        
        # Search history table
        search_history_sql = """
        CREATE TABLE IF NOT EXISTS search_history (
            id SERIAL PRIMARY KEY,
            search_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            market_city VARCHAR(100),
            market_state VARCHAR(10),
            search_criteria JSONB,
            
            -- Results
            properties_found INTEGER,
            properties_analyzed INTEGER,
            high_priority_found INTEGER,
            medium_priority_found INTEGER,
            
            -- Performance
            execution_time_seconds DECIMAL(8,3),
            api_calls_made INTEGER,
            
            -- Status
            search_status VARCHAR(50),
            error_message TEXT
        );
        
        CREATE INDEX IF NOT EXISTS idx_search_history_timestamp ON search_history (search_timestamp);
        CREATE INDEX IF NOT EXISTS idx_search_history_market ON search_history (market_city, market_state);
        CREATE INDEX IF NOT EXISTS idx_search_history_status ON search_history (search_status);
        """
        
        # Daily statistics table
        daily_stats_sql = """
        CREATE TABLE IF NOT EXISTS daily_stats (
            date DATE PRIMARY KEY,
            
            -- Search Activity
            total_searches INTEGER DEFAULT 0,
            total_api_calls INTEGER DEFAULT 0,
            total_properties_analyzed INTEGER DEFAULT 0,
            
            -- Discovery Results
            new_properties_found INTEGER DEFAULT 0,
            high_priority_properties INTEGER DEFAULT 0,
            medium_priority_properties INTEGER DEFAULT 0,
            
            -- Performance Metrics
            avg_search_time DECIMAL(8,3),
            avg_property_score DECIMAL(5,2),
            avg_roi_percentage DECIMAL(5,2),
            
            -- Email Alerts
            alerts_sent INTEGER DEFAULT 0,
            
            -- System Performance
            uptime_percentage DECIMAL(5,2),
            error_count INTEGER DEFAULT 0,
            
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        # Alerts table
        alerts_sql = """
        CREATE TABLE IF NOT EXISTS alerts (
            id SERIAL PRIMARY KEY,
            alert_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            alert_type VARCHAR(50),
            property_id INTEGER REFERENCES properties(id),
            
            -- Alert Content
            subject TEXT,
            message TEXT,
            priority VARCHAR(20),
            
            -- Recipients
            recipient_email VARCHAR(255),
            
            -- Status
            sent_status VARCHAR(20) DEFAULT 'pending',
            sent_timestamp TIMESTAMP,
            error_message TEXT,
            
            -- Metadata
            alert_data JSONB
        );
        
        CREATE INDEX IF NOT EXISTS idx_alerts_timestamp ON alerts (alert_timestamp);
        CREATE INDEX IF NOT EXISTS idx_alerts_status ON alerts (sent_status);
        CREATE INDEX IF NOT EXISTS idx_alerts_type ON alerts (alert_type);
        """
        
        # Search criteria table for multiple search capability
        search_criteria_sql = """
        CREATE TABLE IF NOT EXISTS search_criteria (
            id SERIAL PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            state VARCHAR(10) NOT NULL,
            city VARCHAR(100),

            -- Price criteria
            min_price INTEGER,
            max_price INTEGER,

            -- Property criteria
            min_bedrooms INTEGER,
            max_bedrooms INTEGER,
            min_bathrooms DECIMAL(3,1),
            property_types JSONB,

            -- Investment criteria
            min_roi DECIMAL(5,2),
            search_radius INTEGER DEFAULT 75,
            priority VARCHAR(20) DEFAULT 'MEDIUM',

            -- Status
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

            CONSTRAINT unique_search_name UNIQUE (name)
        );

        CREATE INDEX IF NOT EXISTS idx_search_criteria_state ON search_criteria (state);
        CREATE INDEX IF NOT EXISTS idx_search_criteria_active ON search_criteria (is_active);
        CREATE INDEX IF NOT EXISTS idx_search_criteria_priority ON search_criteria (priority);
        """

        # Execute schema creation
        async with self.pool.acquire() as conn:
            await conn.execute(properties_sql)
            await conn.execute(markets_sql)
            await conn.execute(search_history_sql)
            await conn.execute(daily_stats_sql)
            await conn.execute(alerts_sql)
            await conn.execute(search_criteria_sql)

            self.logger.info("Database schema created successfully")
    
    async def save_property(self, property_data: Dict[str, Any]) -> int:
        """
        Save or update property data
        
        Args:
            property_data: Dictionary containing property information
        
        Returns:
            Property ID (database primary key)
        """
        sql = """
        INSERT INTO properties (
            property_id, address, city, state, zip_code, county,
            bedrooms, bathrooms, square_feet, lot_square_feet, year_built,
            property_type, property_use, estimated_value, assessed_value,
            last_sale_amount, last_sale_date, estimated_rent, fair_market_rent,
            estimated_cash_flow, roi_percentage, investment_score, investment_priority,
            absentee_owner, owner_occupied, vacant, foreclosure,
            latitude, longitude, data_source, raw_data
        ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15,
            $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31
        )
        ON CONFLICT (property_id) 
        DO UPDATE SET
            estimated_rent = EXCLUDED.estimated_rent,
            fair_market_rent = EXCLUDED.fair_market_rent,
            estimated_cash_flow = EXCLUDED.estimated_cash_flow,
            roi_percentage = EXCLUDED.roi_percentage,
            investment_score = EXCLUDED.investment_score,
            investment_priority = EXCLUDED.investment_priority,
            last_updated = CURRENT_TIMESTAMP,
            raw_data = EXCLUDED.raw_data
        RETURNING id;
        """
        
        async with self.pool.acquire() as conn:
            try:
                # Parse last_sale_date if it's a string
                last_sale_date = property_data.get('last_sale_date')
                if isinstance(last_sale_date, str):
                    try:
                        last_sale_date = datetime.strptime(last_sale_date, '%Y-%m-%d').date()
                    except ValueError:
                        last_sale_date = None
                
                property_id = await conn.fetchval(
                    sql,
                    property_data.get('property_id'),
                    property_data.get('address'),
                    property_data.get('city'),
                    property_data.get('state'),
                    property_data.get('zip_code'),
                    property_data.get('county'),
                    property_data.get('bedrooms'),
                    property_data.get('bathrooms'),
                    property_data.get('square_feet'),
                    property_data.get('lot_square_feet'),
                    property_data.get('year_built'),
                    property_data.get('property_type'),
                    property_data.get('property_use'),
                    property_data.get('estimated_value'),
                    property_data.get('assessed_value'),
                    property_data.get('last_sale_amount'),
                    last_sale_date,
                    property_data.get('estimated_rent'),
                    property_data.get('fair_market_rent'),
                    property_data.get('estimated_cash_flow'),
                    property_data.get('roi_percentage'),
                    property_data.get('investment_score'),
                    property_data.get('investment_priority'),
                    property_data.get('absentee_owner'),
                    property_data.get('owner_occupied'),
                    property_data.get('vacant'),
                    property_data.get('foreclosure'),
                    property_data.get('latitude'),
                    property_data.get('longitude'),
                    property_data.get('data_source', 'real_estate_api'),
                    json.dumps(property_data.get('raw_data', {}))
                )
                
                return property_id
                
            except Exception as e:
                self.logger.error(f"Error saving property: {str(e)}")
                raise
    
    async def get_high_priority_properties(
        self, 
        limit: int = 50, 
        days_back: int = 7
    ) -> List[Dict[str, Any]]:
        """Get high priority properties from recent searches"""
        sql = """
        SELECT * FROM properties 
        WHERE investment_priority = 'HIGH'
        AND first_discovered >= $1
        ORDER BY investment_score DESC, roi_percentage DESC
        LIMIT $2;
        """
        
        cutoff_date = datetime.now() - timedelta(days=days_back)
        
        async with self.pool.acquire() as conn:
            rows = await conn.fetch(sql, cutoff_date, limit)
            return [dict(row) for row in rows]
    
    async def update_market_stats(self, market_data: Dict[str, Any]) -> None:
        """Update market statistics"""
        sql = """
        INSERT INTO markets (
            city, state, metro, priority, weight, total_properties_scanned,
            high_priority_found, medium_priority_found, low_priority_found,
            avg_property_score, avg_roi_percentage, avg_property_price,
            fmr_data, fmr_last_updated, last_scanned, scan_count
        ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, 1
        )
        ON CONFLICT (city, state)
        DO UPDATE SET
            total_properties_scanned = markets.total_properties_scanned + EXCLUDED.total_properties_scanned,
            high_priority_found = markets.high_priority_found + EXCLUDED.high_priority_found,
            medium_priority_found = markets.medium_priority_found + EXCLUDED.medium_priority_found,
            low_priority_found = markets.low_priority_found + EXCLUDED.low_priority_found,
            avg_property_score = EXCLUDED.avg_property_score,
            avg_roi_percentage = EXCLUDED.avg_roi_percentage,
            avg_property_price = EXCLUDED.avg_property_price,
            fmr_data = EXCLUDED.fmr_data,
            fmr_last_updated = EXCLUDED.fmr_last_updated,
            last_scanned = EXCLUDED.last_scanned,
            scan_count = markets.scan_count + 1;
        """
        
        async with self.pool.acquire() as conn:
            await conn.execute(
                sql,
                market_data.get('city'),
                market_data.get('state'),
                market_data.get('metro'),
                market_data.get('priority'),
                market_data.get('weight'),
                market_data.get('total_properties_scanned', 0),
                market_data.get('high_priority_found', 0),
                market_data.get('medium_priority_found', 0),
                market_data.get('low_priority_found', 0),
                market_data.get('avg_property_score'),
                market_data.get('avg_roi_percentage'),
                market_data.get('avg_property_price'),
                json.dumps(market_data.get('fmr_data', {})),
                market_data.get('fmr_last_updated'),
                datetime.now(),
            )
    
    async def log_search_history(self, search_data: Dict[str, Any]) -> None:
        """Log search history"""
        sql = """
        INSERT INTO search_history (
            market_city, market_state, search_criteria, properties_found,
            properties_analyzed, high_priority_found, medium_priority_found,
            execution_time_seconds, api_calls_made, search_status, error_message
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11);
        """
        
        async with self.pool.acquire() as conn:
            await conn.execute(
                sql,
                search_data.get('market_city'),
                search_data.get('market_state'),
                json.dumps(search_data.get('search_criteria', {})),
                search_data.get('properties_found', 0),
                search_data.get('properties_analyzed', 0),
                search_data.get('high_priority_found', 0),
                search_data.get('medium_priority_found', 0),
                search_data.get('execution_time_seconds'),
                search_data.get('api_calls_made', 0),
                search_data.get('search_status', 'completed'),
                search_data.get('error_message')
            )
    
    async def update_daily_stats(self, stats_data: Dict[str, Any]) -> None:
        """Update daily statistics"""
        sql = """
        INSERT INTO daily_stats (
            date, total_searches, total_api_calls, total_properties_analyzed,
            new_properties_found, high_priority_properties, medium_priority_properties,
            avg_search_time, avg_property_score, avg_roi_percentage, alerts_sent
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        ON CONFLICT (date)
        DO UPDATE SET
            total_searches = daily_stats.total_searches + EXCLUDED.total_searches,
            total_api_calls = daily_stats.total_api_calls + EXCLUDED.total_api_calls,
            total_properties_analyzed = daily_stats.total_properties_analyzed + EXCLUDED.total_properties_analyzed,
            new_properties_found = daily_stats.new_properties_found + EXCLUDED.new_properties_found,
            high_priority_properties = daily_stats.high_priority_properties + EXCLUDED.high_priority_properties,
            medium_priority_properties = daily_stats.medium_priority_properties + EXCLUDED.medium_priority_properties,
            avg_search_time = EXCLUDED.avg_search_time,
            avg_property_score = EXCLUDED.avg_property_score,
            avg_roi_percentage = EXCLUDED.avg_roi_percentage,
            alerts_sent = daily_stats.alerts_sent + EXCLUDED.alerts_sent,
            updated_at = CURRENT_TIMESTAMP;
        """
        
        async with self.pool.acquire() as conn:
            await conn.execute(
                sql,
                datetime.now().date(),
                stats_data.get('total_searches', 0),
                stats_data.get('total_api_calls', 0),
                stats_data.get('total_properties_analyzed', 0),
                stats_data.get('new_properties_found', 0),
                stats_data.get('high_priority_properties', 0),
                stats_data.get('medium_priority_properties', 0),
                stats_data.get('avg_search_time'),
                stats_data.get('avg_property_score'),
                stats_data.get('avg_roi_percentage'),
                stats_data.get('alerts_sent', 0)
            )
    
    async def save_alert(self, alert_data: Dict[str, Any]) -> int:
        """Save alert to database"""
        sql = """
        INSERT INTO alerts (
            alert_type, property_id, subject, message, priority,
            recipient_email, alert_data
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id;
        """
        
        async with self.pool.acquire() as conn:
            alert_id = await conn.fetchval(
                sql,
                alert_data.get('alert_type'),
                alert_data.get('property_id'),
                alert_data.get('subject'),
                alert_data.get('message'),
                alert_data.get('priority'),
                alert_data.get('recipient_email'),
                json.dumps(alert_data.get('alert_data', {}))
            )
            
            return alert_id
    
    async def get_dashboard_stats(self) -> Dict[str, Any]:
        """Get statistics for dashboard"""
        sql = """
        SELECT 
            COUNT(*) as total_properties,
            COUNT(*) FILTER (WHERE investment_priority = 'HIGH') as high_priority,
            COUNT(*) FILTER (WHERE investment_priority = 'MEDIUM') as medium_priority,
            AVG(roi_percentage) as avg_roi,
            AVG(investment_score) as avg_score,
            COUNT(*) FILTER (WHERE first_discovered >= CURRENT_DATE) as today_found
        FROM properties
        WHERE first_discovered >= CURRENT_DATE - INTERVAL '30 days';
        """
        
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow(sql)
            return dict(row) if row else {}
    
    async def close(self):
        """Close database connection pool"""
        if self.pool:
            await self.pool.close()
            self.logger.info("Database connection pool closed")
