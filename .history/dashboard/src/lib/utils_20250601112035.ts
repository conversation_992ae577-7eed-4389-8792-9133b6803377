import { type ClassValue, clsx } from 'clsx';

export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

export function formatNumber(num: number): string {
  return new Intl.NumberFormat('en-US').format(num);
}

export function formatPercentage(num: number, decimals: number = 1): string {
  return `${num.toFixed(decimals)}%`;
}

export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
}

export function formatRelativeTime(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  }
}

export function getPriorityColor(priority: string): string {
  switch (priority.toUpperCase()) {
    case 'HIGH':
      return 'text-red-600 bg-red-50 border-red-200';
    case 'MEDIUM':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'LOW':
      return 'text-gray-600 bg-gray-50 border-gray-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
}

export function getScoreColor(score: number): string {
  if (score >= 80) {
    return 'text-green-600 bg-green-50';
  } else if (score >= 60) {
    return 'text-yellow-600 bg-yellow-50';
  } else {
    return 'text-red-600 bg-red-50';
  }
}

export function getROIColor(roi: number): string {
  if (roi >= 15) {
    return 'text-green-600';
  } else if (roi >= 10) {
    return 'text-yellow-600';
  } else {
    return 'text-red-600';
  }
}

export function getCashFlowColor(cashFlow: number): string {
  if (cashFlow >= 200) {
    return 'text-green-600';
  } else if (cashFlow >= 100) {
    return 'text-yellow-600';
  } else if (cashFlow >= 0) {
    return 'text-orange-600';
  } else {
    return 'text-red-600';
  }
}

export function truncateAddress(address: string, maxLength: number = 30): string {
  if (address.length <= maxLength) {
    return address;
  }
  return address.substring(0, maxLength) + '...';
}

export function calculateDaysAgo(dateString: string): number {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  return Math.floor(diffInMs / (1000 * 60 * 60 * 24));
}

export function isNewProperty(dateString: string, daysThreshold: number = 1): boolean {
  return calculateDaysAgo(dateString) <= daysThreshold;
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function generatePropertyUrl(property: any): string {
  // Generate a search URL for the property (could be Zillow, Realtor.com, etc.)
  const address = encodeURIComponent(`${property.address} ${property.city} ${property.state}`);
  return `https://www.zillow.com/homes/${address}_rb/`;
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function getPropertyTypeIcon(propertyType: string): string {
  switch (propertyType.toUpperCase()) {
    case 'SINGLE_FAMILY':
      return '🏠';
    case 'MULTI_FAMILY':
      return '🏘️';
    case 'TOWNHOUSE':
      return '🏘️';
    case 'DUPLEX':
      return '🏠';
    case 'CONDO':
      return '🏢';
    default:
      return '🏠';
  }
}
