'use client';

import { useState, useEffect } from 'react';
import { DashboardStats, Property, MarketData } from '@/types';
import { formatCurrency, formatPercentage, formatRelativeTime } from '@/lib/utils';
import StatsCards from '@/components/StatsCards';
import PropertiesTable from '@/components/PropertiesTable';
import MarketsOverview from '@/components/MarketsOverview';
import Header from '@/components/Header';
import LoadingSpinner from '@/components/LoadingSpinner';

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [properties, setProperties] = useState<Property[]>([]);
  const [markets, setMarkets] = useState<MarketData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchDashboardData = async () => {
    try {
      setError(null);
      
      // Fetch dashboard stats
      const statsResponse = await fetch('/api/stats');
      const statsData = await statsResponse.json();
      
      if (statsData.success) {
        setStats(statsData.data);
      } else {
        throw new Error(statsData.error || 'Failed to fetch stats');
      }

      // Fetch recent properties
      const propertiesResponse = await fetch('/api/properties?limit=10&sort_by=discovered_at&sort_order=desc');
      const propertiesData = await propertiesResponse.json();
      
      if (propertiesData.success) {
        setProperties(propertiesData.data.data);
      } else {
        throw new Error(propertiesData.error || 'Failed to fetch properties');
      }

      // Fetch market data
      const marketsResponse = await fetch('/api/markets?days_back=7');
      const marketsData = await marketsResponse.json();
      
      if (marketsData.success) {
        setMarkets(marketsData.data);
      } else {
        throw new Error(marketsData.error || 'Failed to fetch markets');
      }

    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
  };

  useEffect(() => {
    fetchDashboardData();
    
    // Set up auto-refresh every 2 minutes
    const interval = setInterval(fetchDashboardData, 120000);
    
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">⚠️ Error Loading Dashboard</div>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header 
        onRefresh={handleRefresh} 
        refreshing={refreshing}
        lastUpdate={stats?.last_update}
      />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        {stats && <StatsCards stats={stats} />}
        
        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
          {/* Recent Properties */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Recent Properties</h2>
                <p className="text-sm text-gray-600">Latest high-priority investment opportunities</p>
              </div>
              <PropertiesTable properties={properties} compact={true} />
              <div className="px-6 py-4 border-t border-gray-200">
                <a
                  href="/properties"
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  View all properties →
                </a>
              </div>
            </div>
          </div>
          
          {/* Markets Overview */}
          <div className="lg:col-span-1">
            <MarketsOverview markets={markets} />
          </div>
        </div>
        
        {/* Quick Actions */}
        <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <button className="flex items-center justify-center px-4 py-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors">
              <span className="mr-2">🔍</span>
              Search Properties
            </button>
            <button className="flex items-center justify-center px-4 py-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors">
              <span className="mr-2">📊</span>
              View Analytics
            </button>
            <button className="flex items-center justify-center px-4 py-3 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors">
              <span className="mr-2">⚙️</span>
              Settings
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}
