import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/database';
import { SystemStatus } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const client = await pool.connect();
    
    try {
      // Get system status information
      const statusQuery = `
        SELECT 
          COUNT(*) as total_properties,
          COUNT(*) FILTER (WHERE DATE(discovered_at) = CURRENT_DATE) as today_found,
          MAX(discovered_at) as last_scan,
          COUNT(DISTINCT CONCAT(city, ',', state)) as markets_monitored
        FROM property_leads
        WHERE discovered_at >= NOW() - INTERVAL '7 days'
      `;

      const result = await client.query(statusQuery);
      const row = result.rows[0];

      // Calculate next scan time (1 minute from last scan or now)
      const lastScan = row.last_scan ? new Date(row.last_scan) : new Date();
      const nextScan = new Date(lastScan.getTime() + 60000); // 1 minute later
      
      // If next scan is in the past, set it to 1 minute from now
      if (nextScan < new Date()) {
        nextScan.setTime(Date.now() + 60000);
      }

      const status: SystemStatus = {
        database_connected: true,
        api_status: {
          real_estate_api: true, // We'll implement health checks later
          hud_api: true,
          email_service: true,
        },
        monitoring_active: true,
        last_scan: lastScan.toISOString(),
        next_scan: nextScan.toISOString(),
        errors_count: 0, // We'll implement error tracking later
      };

      return NextResponse.json({
        success: true,
        data: status,
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error fetching system status:', error);
    
    // Return a fallback status if database is unavailable
    const fallbackStatus: SystemStatus = {
      database_connected: false,
      api_status: {
        real_estate_api: false,
        hud_api: false,
        email_service: false,
      },
      monitoring_active: false,
      last_scan: new Date().toISOString(),
      next_scan: new Date(Date.now() + 60000).toISOString(),
      errors_count: 1,
    };

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch system status',
        data: fallbackStatus,
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    if (action === 'trigger_scan') {
      // This would trigger a manual property scan
      // For now, we'll just return success
      return NextResponse.json({
        success: true,
        message: 'Property scan triggered successfully',
      });
    }

    if (action === 'restart_monitoring') {
      // This would restart the monitoring system
      return NextResponse.json({
        success: true,
        message: 'Monitoring system restart initiated',
      });
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Unknown action',
      },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error handling system action:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to execute system action',
      },
      { status: 500 }
    );
  }
}
