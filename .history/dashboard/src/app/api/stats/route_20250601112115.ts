import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/database';
import { DashboardStats } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const client = await pool.connect();
    
    try {
      // Get overall statistics
      const statsQuery = `
        SELECT 
          COUNT(*) as total_properties,
          COUNT(*) FILTER (WHERE investment_priority = 'HIGH') as high_priority,
          COUNT(*) FILTER (WHERE investment_priority = 'MEDIUM') as medium_priority,
          COUNT(*) FILTER (WHERE investment_priority = 'LOW') as low_priority,
          COUNT(*) FILTER (WHERE DATE(discovered_at) = CURRENT_DATE) as today_found,
          COALESCE(AVG(roi_percentage), 0) as avg_roi,
          COALESCE(AVG(cash_flow), 0) as avg_cash_flow,
          COUNT(DISTINCT CONCAT(city, ',', state)) as markets_monitored,
          MAX(discovered_at) as last_update
        FROM property_leads
        WHERE discovered_at >= NOW() - INTERVAL '30 days'
      `;

      const result = await client.query(statsQuery);
      const row = result.rows[0];

      const stats: DashboardStats = {
        total_properties: parseInt(row.total_properties),
        high_priority: parseInt(row.high_priority),
        medium_priority: parseInt(row.medium_priority),
        low_priority: parseInt(row.low_priority),
        today_found: parseInt(row.today_found),
        avg_roi: parseFloat(row.avg_roi),
        avg_cash_flow: parseFloat(row.avg_cash_flow),
        markets_monitored: parseInt(row.markets_monitored),
        last_update: row.last_update ? row.last_update.toISOString() : new Date().toISOString(),
      };

      return NextResponse.json({
        success: true,
        data: stats,
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch dashboard statistics',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // This could be used to refresh/recalculate stats
    return NextResponse.json({
      success: true,
      message: 'Stats refreshed successfully',
    });

  } catch (error) {
    console.error('Error refreshing stats:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to refresh statistics',
      },
      { status: 500 }
    );
  }
}
