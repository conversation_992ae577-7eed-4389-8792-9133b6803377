import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/database';
import { Property, FilterOptions, PaginatedResponse } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const priority = searchParams.get('priority') || 'ALL';
    const city = searchParams.get('city');
    const state = searchParams.get('state');
    const minRoi = searchParams.get('min_roi') ? parseFloat(searchParams.get('min_roi')!) : undefined;
    const maxPrice = searchParams.get('max_price') ? parseInt(searchParams.get('max_price')!) : undefined;
    const minCashFlow = searchParams.get('min_cash_flow') ? parseInt(searchParams.get('min_cash_flow')!) : undefined;
    const daysBack = parseInt(searchParams.get('days_back') || '30');
    const sortBy = searchParams.get('sort_by') || 'investment_score';
    const sortOrder = searchParams.get('sort_order') || 'desc';

    // Build WHERE clause
    const whereConditions: string[] = [];
    const queryParams: any[] = [];
    let paramIndex = 1;

    // Date filter
    whereConditions.push(`first_discovered >= NOW() - INTERVAL '${daysBack} days'`);

    // Priority filter
    if (priority !== 'ALL') {
      whereConditions.push(`investment_priority = $${paramIndex}`);
      queryParams.push(priority);
      paramIndex++;
    }

    // City filter
    if (city) {
      whereConditions.push(`UPPER(city) = UPPER($${paramIndex})`);
      queryParams.push(city);
      paramIndex++;
    }

    // State filter
    if (state) {
      whereConditions.push(`UPPER(state) = UPPER($${paramIndex})`);
      queryParams.push(state);
      paramIndex++;
    }

    // ROI filter
    if (minRoi !== undefined) {
      whereConditions.push(`roi_percentage >= $${paramIndex}`);
      queryParams.push(minRoi);
      paramIndex++;
    }

    // Price filter
    if (maxPrice !== undefined) {
      whereConditions.push(`estimated_value <= $${paramIndex}`);
      queryParams.push(maxPrice);
      paramIndex++;
    }

    // Cash flow filter
    if (minCashFlow !== undefined) {
      whereConditions.push(`estimated_cash_flow >= $${paramIndex}`);
      queryParams.push(minCashFlow);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Validate sort column
    const validSortColumns = ['investment_score', 'roi_percentage', 'estimated_cash_flow', 'first_discovered', 'estimated_value'];
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'investment_score';
    const sortDirection = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM properties
      ${whereClause}
    `;

    const countResult = await pool.query(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].total);

    // Get paginated results
    const offset = (page - 1) * limit;
    const dataQuery = `
      SELECT
        id, address, city, state, zip_code, estimated_value, bedrooms, bathrooms,
        square_feet, lot_square_feet as lot_size, year_built, property_type,
        estimated_rent, investment_score, investment_priority, roi_percentage,
        estimated_cash_flow, first_discovered as discovered_at
      FROM properties
      ${whereClause}
      ORDER BY ${sortColumn} ${sortDirection}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);
    const dataResult = await pool.query(dataQuery, queryParams);

    const properties: Property[] = dataResult.rows.map(row => ({
      ...row,
      discovered_at: row.discovered_at.toISOString(),
    }));

    const response: PaginatedResponse<Property> = {
      data: properties,
      total,
      page,
      limit,
      total_pages: Math.ceil(total / limit),
    };

    return NextResponse.json({
      success: true,
      data: response,
    });

  } catch (error) {
    console.error('Error fetching properties:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch properties',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // This would be used for manual property addition
    // Implementation depends on requirements
    
    return NextResponse.json({
      success: true,
      message: 'Property added successfully',
    });

  } catch (error) {
    console.error('Error adding property:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to add property',
      },
      { status: 500 }
    );
  }
}
