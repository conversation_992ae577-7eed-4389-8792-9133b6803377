import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/database';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const propertyId = params.id;
    
    if (!propertyId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Property ID is required',
        },
        { status: 400 }
      );
    }

    const client = await pool.connect();
    
    try {
      // Get detailed property information
      const query = `
        SELECT
          id, property_id, address, city, state, zip_code, county, estimated_value,
          bedrooms, bathrooms, square_feet, lot_square_feet as lot_size, year_built,
          property_type, estimated_rent, investment_score, investment_priority,
          roi_percentage, estimated_cash_flow, first_discovered as discovered_at,
          latitude, longitude, assessed_value, last_sale_amount, last_sale_date,
          owner_occupied, absentee_owner, vacant, foreclosure, data_source, raw_data,
          enhanced_data, enhanced_data_updated_at
        FROM properties
        WHERE id = $1
      `;

      const result = await client.query(query, [propertyId]);
      
      if (result.rows.length === 0) {
        return NextResponse.json(
          {
            success: false,
            error: 'Property not found',
          },
          { status: 404 }
        );
      }

      const property = result.rows[0];
      
      // Parse raw_data if it exists and contains additional information
      let enhancedData = {};
      if (property.raw_data) {
        try {
          const rawData = typeof property.raw_data === 'string' 
            ? JSON.parse(property.raw_data) 
            : property.raw_data;
          enhancedData = rawData;
        } catch (e) {
          console.warn('Failed to parse raw_data for property', propertyId);
        }
      }

      // Format the response
      const formattedProperty = {
        ...property,
        discovered_at: property.discovered_at.toISOString(),
        last_sale_date: property.last_sale_date ? property.last_sale_date.toISOString() : null,
        ...enhancedData,
      };

      return NextResponse.json({
        success: true,
        data: formattedProperty,
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error fetching property details:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch property details',
      },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const propertyId = params.id;
    const body = await request.json();
    
    if (!propertyId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Property ID is required',
        },
        { status: 400 }
      );
    }

    const client = await pool.connect();
    
    try {
      // Update property information
      const updateFields = [];
      const values = [];
      let paramIndex = 1;

      // Allow updating specific fields
      const allowedFields = [
        'investment_priority', 'investment_score', 'estimated_rent',
        'roi_percentage', 'cash_flow', 'notes'
      ];

      for (const field of allowedFields) {
        if (body[field] !== undefined) {
          updateFields.push(`${field} = $${paramIndex}`);
          values.push(body[field]);
          paramIndex++;
        }
      }

      if (updateFields.length === 0) {
        return NextResponse.json(
          {
            success: false,
            error: 'No valid fields to update',
          },
          { status: 400 }
        );
      }

      // Add updated_at timestamp
      updateFields.push(`updated_at = NOW()`);
      values.push(propertyId);

      const query = `
        UPDATE property_leads 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING id, investment_priority, investment_score, updated_at
      `;

      const result = await client.query(query, values);
      
      if (result.rows.length === 0) {
        return NextResponse.json(
          {
            success: false,
            error: 'Property not found',
          },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: result.rows[0],
        message: 'Property updated successfully',
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error updating property:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update property',
      },
      { status: 500 }
    );
  }
}
