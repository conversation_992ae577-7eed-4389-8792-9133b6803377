import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/database';

// Zillow API integration via RapidAPI
async function fetchZillowData(address: string, city: string, state: string) {
  try {
    const zillowApiKey = process.env.ZILLOW_API_KEY;
    
    if (!zillowApiKey) {
      console.warn('Zillow API key not configured');
      return null;
    }

    // Format address for Zillow API
    const searchAddress = `${address}, ${city}, ${state}`;
    
    // First, search for the property to get Zillow property ID
    const searchResponse = await fetch('https://zillow-com1.p.rapidapi.com/propertyExtendedSearch', {
      method: 'GET',
      headers: {
        'X-RapidAPI-Key': zillowApiKey,
        'X-RapidAPI-Host': 'zillow-com1.p.rapidapi.com'
      },
      // Note: This is a simplified example. The actual Zillow API parameters may differ
    });

    if (!searchResponse.ok) {
      console.warn('Zillow search API request failed:', searchResponse.status);
      return null;
    }

    const searchData = await searchResponse.json();
    
    // Extract property details from Zillow response
    // This is a mock structure - actual Zillow API response structure may differ
    const zillowData = {
      zestimate: searchData.zestimate || null,
      rent_estimate: searchData.rentZestimate || null,
      price_history: searchData.priceHistory || [],
      neighborhood_info: {
        walk_score: searchData.walkScore || null,
        school_rating: searchData.schoolRating || null,
        crime_rating: searchData.crimeRating || null,
      },
      last_updated: new Date().toISOString(),
    };

    return zillowData;

  } catch (error) {
    console.error('Error fetching Zillow data:', error);
    return null;
  }
}

// Real Estate API integration for additional property details
async function fetchRealEstateApiData(propertyId: string, address: string) {
  try {
    const apiKey = process.env.REAL_ESTATE_API_KEY;
    const apiUrl = process.env.REAL_ESTATE_API_URL;
    
    if (!apiKey || !apiUrl) {
      console.warn('Real Estate API not configured');
      return null;
    }

    // Fetch detailed property information
    const response = await fetch(`${apiUrl}/v2/PropertyDetail`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        address: address,
      }),
    });

    if (!response.ok) {
      console.warn('Real Estate API request failed:', response.status);
      return null;
    }

    const data = await response.json();
    
    // Extract relevant information
    const enhancedData = {
      owner_info: data.ownerInfo ? {
        name: `${data.ownerInfo.owner1FirstName || ''} ${data.ownerInfo.owner1LastName || ''}`.trim(),
        mailing_address: data.ownerInfo.mailAddress?.addressFormat || null,
        years_owned: data.ownerInfo.yearsOwned || null,
      } : null,
      tax_info: data.taxInfo ? {
        assessed_value: data.taxInfo.assessedValue || null,
        tax_amount: data.taxInfo.taxAmount || null,
        tax_year: data.taxInfo.taxYear || null,
      } : null,
      comparable_properties: data.comps ? data.comps.slice(0, 5).map((comp: any) => ({
        address: comp.address,
        distance: comp.distance,
        sale_price: comp.lastSaleAmount,
        sale_date: comp.lastSaleDate,
      })) : [],
      last_updated: new Date().toISOString(),
    };

    return enhancedData;

  } catch (error) {
    console.error('Error fetching Real Estate API data:', error);
    return null;
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const propertyId = params.id;
    
    if (!propertyId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Property ID is required',
        },
        { status: 400 }
      );
    }

    const client = await pool.connect();
    
    try {
      // Get basic property information
      const query = `
        SELECT id, address, city, state, zip_code, property_id
        FROM properties
        WHERE id = $1
      `;

      const result = await client.query(query, [propertyId]);
      
      if (result.rows.length === 0) {
        return NextResponse.json(
          {
            success: false,
            error: 'Property not found',
          },
          { status: 404 }
        );
      }

      const property = result.rows[0];
      
      // Fetch enhanced data from external APIs
      const [zillowData, realEstateApiData] = await Promise.all([
        fetchZillowData(property.address, property.city, property.state),
        fetchRealEstateApiData(property.property_id, property.address),
      ]);

      // Combine enhanced data
      const enhancedData = {
        zillow_data: zillowData,
        real_estate_api_data: realEstateApiData,
      };

      // Update the property record with enhanced data
      const updateQuery = `
        UPDATE properties
        SET
          enhanced_data = $1,
          enhanced_data_updated_at = NOW()
        WHERE id = $2
      `;

      await client.query(updateQuery, [JSON.stringify(enhancedData), propertyId]);

      return NextResponse.json({
        success: true,
        data: enhancedData,
        message: 'Enhanced property data fetched successfully',
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error fetching enhanced property data:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch enhanced property data',
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const propertyId = params.id;
    const body = await request.json();
    const { force_refresh = false } = body;
    
    const client = await pool.connect();
    
    try {
      // Check if we have recent enhanced data (unless force refresh)
      if (!force_refresh) {
        const checkQuery = `
          SELECT enhanced_data, enhanced_data_updated_at
          FROM property_leads
          WHERE id = $1 
          AND enhanced_data_updated_at > NOW() - INTERVAL '24 hours'
        `;

        const checkResult = await client.query(checkQuery, [propertyId]);
        
        if (checkResult.rows.length > 0 && checkResult.rows[0].enhanced_data) {
          return NextResponse.json({
            success: true,
            data: JSON.parse(checkResult.rows[0].enhanced_data),
            message: 'Using cached enhanced data',
            cached: true,
          });
        }
      }

      // If no recent data or force refresh, fetch new data
      // This would trigger the GET endpoint logic
      const response = await GET(request, { params });
      return response;

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error handling enhanced data request:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process enhanced data request',
      },
      { status: 500 }
    );
  }
}
