#!/bin/bash

# Section 8 Property Monitor - Development System Startup Script
# This script starts all components for development/testing

echo "🚀 Starting Section 8 Property Monitor (Development Mode)..."
echo "============================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Function to start a service in background
start_service() {
    local name=$1
    local command=$2
    local port=$3
    local log_file=$4
    
    echo -e "${BLUE}Starting $name...${NC}"
    
    if [ ! -z "$port" ] && port_in_use $port; then
        echo -e "${YELLOW}⚠️  Port $port is already in use. $name may already be running.${NC}"
        return 1
    fi
    
    # Start the service in background
    nohup $command > "$log_file" 2>&1 &
    local pid=$!
    
    # Wait a moment and check if process is still running
    sleep 2
    if kill -0 $pid 2>/dev/null; then
        echo -e "${GREEN}✅ $name started successfully (PID: $pid)${NC}"
        echo $pid > "pids/${name,,}.pid"
        return 0
    else
        echo -e "${RED}❌ Failed to start $name${NC}"
        return 1
    fi
}

# Create directories for logs and PIDs
mkdir -p logs
mkdir -p pids

echo -e "${BLUE}Checking prerequisites...${NC}"

# Check Python
if ! command_exists python3; then
    echo -e "${RED}❌ Python 3 is not installed${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Python 3 found${NC}"

# Check Node.js
if ! command_exists node; then
    echo -e "${RED}❌ Node.js is not installed${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Node.js found${NC}"

# Check npm
if ! command_exists npm; then
    echo -e "${RED}❌ npm is not installed${NC}"
    exit 1
fi
echo -e "${GREEN}✅ npm found${NC}"

# Check virtual environment
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}⚠️  Virtual environment not found. Creating one...${NC}"
    python3 -m venv venv
fi

# Activate virtual environment
echo -e "${BLUE}Activating virtual environment...${NC}"
source venv/bin/activate

# Check .env file
if [ ! -f ".env" ]; then
    echo -e "${RED}❌ .env file not found. Please create it with required environment variables.${NC}"
    echo -e "${YELLOW}See SECTION8_MONITOR_SETUP_GUIDE.md for details.${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Environment file found${NC}"

# Install Python dependencies
echo -e "${BLUE}Installing/updating Python dependencies...${NC}"
pip install -r requirements.txt > logs/pip_install.log 2>&1
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Python dependencies installed${NC}"
else
    echo -e "${RED}❌ Failed to install Python dependencies. Check logs/pip_install.log${NC}"
    exit 1
fi

# Install dashboard dependencies
echo -e "${BLUE}Installing dashboard dependencies...${NC}"
if [ -d "dashboard" ]; then
    cd dashboard
    npm install > ../logs/dashboard_npm_install.log 2>&1
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Dashboard dependencies installed${NC}"
    else
        echo -e "${RED}❌ Failed to install dashboard dependencies${NC}"
    fi
    cd ..
fi

# Install live dashboard dependencies
echo -e "${BLUE}Installing live dashboard dependencies...${NC}"
if [ -d "live-dashboard" ]; then
    cd live-dashboard
    npm install > ../logs/live_dashboard_npm_install.log 2>&1
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Live dashboard dependencies installed${NC}"
    else
        echo -e "${RED}❌ Failed to install live dashboard dependencies${NC}"
    fi
    cd ..
fi

echo ""
echo -e "${BLUE}Starting services...${NC}"
echo "===================="

# Start Python monitoring system
start_service "Monitor" "python3 production_monitor.py" "" "logs/monitor.log"

# Start Next.js dashboard
if [ -d "dashboard" ]; then
    start_service "Dashboard" "npm --prefix dashboard run dev" "3000" "logs/dashboard.log"
fi

# Start live monitoring dashboard
if [ -d "live-dashboard" ]; then
    start_service "LiveDashboard" "npm --prefix live-dashboard start" "8080" "logs/live_dashboard.log"
fi

# Start additional monitoring (optional)
start_service "Section8Monitor" "python3 section8_monitor_pro.py" "" "logs/section8_monitor.log"

# Wait a moment for services to fully start
echo ""
echo -e "${BLUE}Waiting for services to initialize...${NC}"
sleep 5

echo ""
echo -e "${GREEN}🎉 Development system startup complete!${NC}"
echo "========================================"
echo ""
echo -e "${BLUE}Access Points:${NC}"
echo "📊 Main Dashboard:     http://localhost:3000"
echo "📈 Live Monitor:       http://localhost:8080"
echo "🔧 API Health:         http://localhost:5000/api/health"
echo ""
echo -e "${BLUE}Log Files:${NC}"
echo "📝 Monitor:            logs/monitor.log"
echo "📝 Dashboard:          logs/dashboard.log"
echo "📝 Live Dashboard:     logs/live_dashboard.log"
echo "📝 Section 8 Monitor:  logs/section8_monitor.log"
echo ""
echo -e "${BLUE}Management:${NC}"
echo "🛑 Stop all services:  ./stop_dev_system.sh"
echo "📊 View logs:          tail -f logs/monitor.log"
echo "🔍 Check status:       ps aux | grep -E '(python|node)'"
echo ""
echo -e "${YELLOW}💡 Tip: Keep this terminal open to see startup messages.${NC}"
echo -e "${YELLOW}💡 Open a new terminal for additional commands.${NC}"
echo ""
echo -e "${GREEN}Happy monitoring! 🏠💰${NC}"
