#!/usr/bin/env python3
"""
Section 8 Property Monitor - Production Version
Optimized for PythonAnywhere deployment with 1-minute intervals
"""

import asyncio
import asyncpg
import aiohttp
import json
import logging
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import traceback
from pathlib import Path
import resend

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/section8_monitor_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)

@dataclass
class PropertyLead:
    """Data class for property leads"""
    address: str
    city: str
    state: str
    zip_code: str
    estimated_value: int
    bedrooms: int
    bathrooms: float
    square_feet: int
    lot_size: int
    year_built: int
    property_type: str
    days_on_market: int
    estimated_rent: int
    investment_score: int
    investment_priority: str
    roi_percentage: float
    cash_flow: int
    fmr_2br: int
    fmr_3br: int
    discovered_at: datetime

class Section8ProductionMonitor:
    """Production Section 8 Property Monitor"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = self.load_config()
        self.session: Optional[aiohttp.ClientSession] = None
        self.db_pool: Optional[asyncpg.Pool] = None
        self.running = False
        
        # API configuration
        self.real_estate_api_key = os.getenv('REAL_ESTATE_API_KEY')
        self.real_estate_api_url = os.getenv('REAL_ESTATE_API_URL')
        self.hud_api_key = os.getenv('HUD_API_KEY')
        self.hud_api_url = os.getenv('HUD_API_URL')
        self.resend_api_key = os.getenv('RESEND_API_KEY')
        self.alert_email = os.getenv('ALERT_EMAIL')
        self.database_url = os.getenv('DATABASE_URL')
        
        # Initialize Resend client
        resend.api_key = self.resend_api_key
        
        # Performance tracking
        self.daily_stats = {
            'properties_analyzed': 0,
            'high_priority_found': 0,
            'alerts_sent': 0,
            'api_calls_made': 0,
            'markets_processed': 0,
            'errors_count': 0
        }
        
        # Top 15-20 cities for Section 8 investment
        self.target_cities = [
            {"city": "DETROIT", "state": "MI", "priority": "VERY_HIGH"},
            {"city": "CLEVELAND", "state": "OH", "priority": "VERY_HIGH"},
            {"city": "PITTSBURGH", "state": "PA", "priority": "VERY_HIGH"},
            {"city": "BUFFALO", "state": "NY", "priority": "VERY_HIGH"},
            {"city": "MILWAUKEE", "state": "WI", "priority": "VERY_HIGH"},
            {"city": "AKRON", "state": "OH", "priority": "HIGH"},
            {"city": "TOLEDO", "state": "OH", "priority": "HIGH"},
            {"city": "YOUNGSTOWN", "state": "OH", "priority": "HIGH"},
            {"city": "DAYTON", "state": "OH", "priority": "HIGH"},
            {"city": "FLINT", "state": "MI", "priority": "HIGH"},
            {"city": "BIRMINGHAM", "state": "AL", "priority": "HIGH"},
            {"city": "MEMPHIS", "state": "TN", "priority": "HIGH"},
            {"city": "JACKSON", "state": "MS", "priority": "HIGH"},
            {"city": "NEW BERN", "state": "NC", "priority": "HIGH"},
            {"city": "WILMINGTON", "state": "NC", "priority": "HIGH"},
            {"city": "FORT WAYNE", "state": "IN", "priority": "MEDIUM"},
            {"city": "EVANSVILLE", "state": "IN", "priority": "MEDIUM"},
            {"city": "SPRINGFIELD", "state": "IL", "priority": "MEDIUM"},
            {"city": "ROCKFORD", "state": "IL", "priority": "MEDIUM"},
            {"city": "PEORIA", "state": "IL", "priority": "MEDIUM"}
        ]
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from config.json"""
        try:
            with open('config.json', 'r') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load config: {e}")
            return {}
    
    async def initialize(self):
        """Initialize database and HTTP session"""
        try:
            # Create logs directory
            Path("logs").mkdir(exist_ok=True)
            
            # Initialize HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={'User-Agent': 'Section8InvestorPro/4.0'}
            )
            
            # Initialize database connection pool
            self.db_pool = await asyncpg.create_pool(
                self.database_url,
                min_size=5,
                max_size=15,
                command_timeout=60
            )
            
            # Create database schema
            await self.create_database_schema()
            
            self.logger.info("Production monitor initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Initialization failed: {e}")
            raise
    
    async def create_database_schema(self):
        """Create database tables if they don't exist"""
        schema_sql = """
        CREATE TABLE IF NOT EXISTS property_leads (
            id SERIAL PRIMARY KEY,
            address VARCHAR(255) NOT NULL,
            city VARCHAR(100) NOT NULL,
            state VARCHAR(2) NOT NULL,
            zip_code VARCHAR(10),
            estimated_value INTEGER,
            bedrooms INTEGER,
            bathrooms DECIMAL(3,1),
            square_feet INTEGER,
            lot_size INTEGER,
            year_built INTEGER,
            property_type VARCHAR(50),
            days_on_market INTEGER,
            estimated_rent INTEGER,
            investment_score INTEGER,
            investment_priority VARCHAR(20),
            roi_percentage DECIMAL(5,2),
            cash_flow INTEGER,
            fmr_2br INTEGER,
            fmr_3br INTEGER,
            discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(address, city, state)
        );
        
        CREATE INDEX IF NOT EXISTS idx_property_leads_priority ON property_leads (investment_priority);
        CREATE INDEX IF NOT EXISTS idx_property_leads_discovered ON property_leads (discovered_at);
        CREATE INDEX IF NOT EXISTS idx_property_leads_city_state ON property_leads (city, state);
        CREATE INDEX IF NOT EXISTS idx_property_leads_score ON property_leads (investment_score);
        
        CREATE TABLE IF NOT EXISTS monitoring_stats (
            id SERIAL PRIMARY KEY,
            date DATE DEFAULT CURRENT_DATE,
            properties_analyzed INTEGER DEFAULT 0,
            high_priority_found INTEGER DEFAULT 0,
            alerts_sent INTEGER DEFAULT 0,
            api_calls_made INTEGER DEFAULT 0,
            markets_processed INTEGER DEFAULT 0,
            errors_count INTEGER DEFAULT 0,
            UNIQUE(date)
        );
        
        CREATE TABLE IF NOT EXISTS email_alerts (
            id SERIAL PRIMARY KEY,
            property_id INTEGER REFERENCES property_leads(id),
            alert_type VARCHAR(50),
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            email_status VARCHAR(20),
            error_message TEXT
        );
        """
        
        async with self.db_pool.acquire() as conn:
            await conn.execute(schema_sql)
            self.logger.info("Database schema created/verified")
    
    async def search_properties_in_city(self, city: str, state: str) -> List[Dict[str, Any]]:
        """Search for properties in a specific city using correct RealEstateAPI format"""
        all_properties = []

        # Search for both SFR and MFR properties separately since API doesn't accept comma-separated values
        property_types = ["SFR", "MFR"]

        for property_type in property_types:
            try:
                url = f"{self.real_estate_api_url}/v2/PropertySearch"

                # Correct payload format based on working API test
                payload = {
                    "city": city,
                    "state": state,
                    "beds_min": 2,
                    "beds_max": 5,
                    "value_min": 30000,
                    "value_max": 400000,
                    "property_type": property_type,
                    "limit": 25
                }

                headers = {
                    'X-API-Key': self.real_estate_api_key,  # Correct header format
                    'Content-Type': 'application/json'
                }

                # Use POST request as per API documentation
                async with self.session.post(url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.daily_stats['api_calls_made'] += 1

                        # Handle RealEstateAPI response format
                        if isinstance(data, dict) and 'data' in data:
                            properties = data['data']
                            all_properties.extend(properties)
                            self.logger.info(f"Found {len(properties)} {property_type} properties in {city}, {state}")
                        else:
                            self.logger.warning(f"Unexpected response format for {city}, {state} ({property_type})")

                    elif response.status == 401:
                        self.logger.error(f"Authentication failed for {city}, {state} - check API key")
                    elif response.status == 403:
                        self.logger.error(f"Access forbidden for {city}, {state} - check API permissions")
                    elif response.status == 429:
                        self.logger.warning(f"Rate limited for {city}, {state} - waiting...")
                        await asyncio.sleep(5)
                    else:
                        self.logger.warning(f"API request failed for {city}, {state} ({property_type}): {response.status}")

                # Rate limiting between property type searches
                await asyncio.sleep(1)

            except Exception as e:
                self.logger.error(f"Error searching {property_type} properties in {city}, {state}: {e}")
                self.daily_stats['errors_count'] += 1
                continue

        self.logger.info(f"Total properties found in {city}, {state}: {len(all_properties)}")
        return all_properties
    
    async def get_hud_fmr_data(self, city: str, state: str) -> Dict[str, int]:
        """Get HUD Fair Market Rent data for a city"""
        try:
            url = f"{self.hud_api_url}/fmr/listCounties/{state}"
            
            headers = {
                'Authorization': f'Bearer {self.hud_api_key}',
                'Content-Type': 'application/json'
            }
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    # Find FMR data for the city (simplified)
                    return {"fmr_2br": 800, "fmr_3br": 1000}  # Default values
                else:
                    return {"fmr_2br": 800, "fmr_3br": 1000}  # Default fallback
                    
        except Exception as e:
            self.logger.error(f"Error getting FMR data for {city}, {state}: {e}")
            return {"fmr_2br": 800, "fmr_3br": 1000}  # Default fallback

    def calculate_investment_score(self, property_data: Dict[str, Any], fmr_data: Dict[str, int]) -> Dict[str, Any]:
        """Calculate investment score and analysis for a property"""
        try:
            # Extract property details
            estimated_value = property_data.get('estimatedValue', 0)
            bedrooms = property_data.get('bedrooms', 0)
            bathrooms = property_data.get('bathrooms', 0)
            square_feet = property_data.get('squareFeet', 0)
            days_on_market = property_data.get('daysOnMarket', 0)

            # Estimate rent based on FMR
            if bedrooms >= 3:
                estimated_rent = fmr_data['fmr_3br']
            else:
                estimated_rent = fmr_data['fmr_2br']

            # Calculate basic investment metrics
            if estimated_value > 0:
                # Monthly cash flow estimate (simplified)
                monthly_expenses = estimated_value * 0.01  # 1% rule for expenses
                cash_flow = estimated_rent - monthly_expenses

                # ROI calculation
                annual_rent = estimated_rent * 12
                roi_percentage = (annual_rent / estimated_value) * 100 if estimated_value > 0 else 0

                # Investment score calculation (0-100)
                score = 0

                # ROI component (40% weight)
                if roi_percentage >= 15:
                    score += 40
                elif roi_percentage >= 12:
                    score += 30
                elif roi_percentage >= 10:
                    score += 20
                elif roi_percentage >= 8:
                    score += 10

                # Cash flow component (30% weight)
                if cash_flow >= 300:
                    score += 30
                elif cash_flow >= 200:
                    score += 25
                elif cash_flow >= 100:
                    score += 20
                elif cash_flow >= 50:
                    score += 15
                elif cash_flow >= 0:
                    score += 10

                # Price component (20% weight)
                if estimated_value <= 100000:
                    score += 20
                elif estimated_value <= 150000:
                    score += 15
                elif estimated_value <= 200000:
                    score += 10
                elif estimated_value <= 300000:
                    score += 5

                # Days on market component (10% weight) - prefer newer listings
                if days_on_market == 0:
                    score += 10
                elif days_on_market == 1:
                    score += 8
                elif days_on_market <= 3:
                    score += 5
                elif days_on_market <= 7:
                    score += 3

                # Determine priority
                if score >= 80:
                    priority = "HIGH"
                elif score >= 60:
                    priority = "MEDIUM"
                else:
                    priority = "LOW"

                return {
                    'investment_score': int(score),
                    'investment_priority': priority,
                    'roi_percentage': round(roi_percentage, 2),
                    'estimated_rent': int(estimated_rent),
                    'cash_flow': int(cash_flow)
                }
            else:
                return {
                    'investment_score': 0,
                    'investment_priority': 'LOW',
                    'roi_percentage': 0.0,
                    'estimated_rent': int(estimated_rent),
                    'cash_flow': 0
                }

        except Exception as e:
            self.logger.error(f"Error calculating investment score: {e}")
            return {
                'investment_score': 0,
                'investment_priority': 'LOW',
                'roi_percentage': 0.0,
                'estimated_rent': 0,
                'cash_flow': 0
            }

    async def save_property_to_database(self, property_data: Dict[str, Any], analysis: Dict[str, Any], fmr_data: Dict[str, int]):
        """Save property to database with correct RealEstateAPI data structure"""
        try:
            # Extract data from RealEstateAPI response structure
            property_info = property_data.get('propertyInfo', {})
            address_info = property_info.get('address', {})

            # Handle address - could be string or dict
            if isinstance(address_info, dict):
                full_address = address_info.get('address', '')
                city = address_info.get('city', '')
                state = address_info.get('state', '')
                zip_code = address_info.get('zip', '')
            else:
                # Fallback if address is a string
                full_address = str(address_info)
                city = property_data.get('city', '')
                state = property_data.get('state', '')
                zip_code = property_data.get('zip', '')

            insert_sql = """
            INSERT INTO property_leads (
                address, city, state, zip_code, estimated_value, bedrooms, bathrooms,
                square_feet, lot_size, year_built, property_type, days_on_market,
                estimated_rent, investment_score, investment_priority, roi_percentage,
                cash_flow, fmr_2br, fmr_3br
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
            ON CONFLICT (address, city, state) DO UPDATE SET
                estimated_value = EXCLUDED.estimated_value,
                investment_score = EXCLUDED.investment_score,
                investment_priority = EXCLUDED.investment_priority,
                roi_percentage = EXCLUDED.roi_percentage,
                cash_flow = EXCLUDED.cash_flow,
                days_on_market = EXCLUDED.days_on_market
            RETURNING id
            """

            async with self.db_pool.acquire() as conn:
                result = await conn.fetchrow(
                    insert_sql,
                    full_address,
                    city,
                    state,
                    zip_code,
                    property_info.get('estimatedValue', 0),
                    property_info.get('bedrooms', 0),
                    property_info.get('bathrooms', 0.0),
                    property_info.get('livingSquareFeet', 0),
                    property_info.get('lotSquareFeet', 0),
                    property_info.get('yearBuilt', 0),
                    property_data.get('propertyType', ''),
                    property_info.get('daysOnMarket', 0),
                    analysis['estimated_rent'],
                    analysis['investment_score'],
                    analysis['investment_priority'],
                    analysis['roi_percentage'],
                    analysis['cash_flow'],
                    fmr_data['fmr_2br'],
                    fmr_data['fmr_3br']
                )

                return result['id'] if result else None

        except Exception as e:
            self.logger.error(f"Error saving property to database: {e}")
            return None

    async def send_property_alert(self, property_data: Dict[str, Any], analysis: Dict[str, Any], property_id: int):
        """Send email alert for high-priority property"""
        try:
            if analysis['investment_priority'] not in ['HIGH']:
                return False

            # Create email content
            subject = f"🏠 HIGH PRIORITY Section 8 Property Alert - {property_data.get('city', '')}, {property_data.get('state', '')}"

            html_content = f"""
            <html>
            <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #2c5aa0;">🏠 High Priority Section 8 Investment Opportunity</h2>

                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #28a745; margin-top: 0;">Property Details</h3>
                    <p><strong>Address:</strong> {property_data.get('address', 'N/A')}</p>
                    <p><strong>City, State:</strong> {property_data.get('city', 'N/A')}, {property_data.get('state', 'N/A')}</p>
                    <p><strong>Estimated Value:</strong> ${property_data.get('estimatedValue', 0):,}</p>
                    <p><strong>Bedrooms:</strong> {property_data.get('bedrooms', 0)}</p>
                    <p><strong>Bathrooms:</strong> {property_data.get('bathrooms', 0)}</p>
                    <p><strong>Square Feet:</strong> {property_data.get('squareFeet', 0):,}</p>
                    <p><strong>Days on Market:</strong> {property_data.get('daysOnMarket', 0)}</p>
                </div>

                <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #28a745; margin-top: 0;">Investment Analysis</h3>
                    <p><strong>Investment Score:</strong> {analysis['investment_score']}/100</p>
                    <p><strong>Priority:</strong> <span style="color: #dc3545; font-weight: bold;">{analysis['investment_priority']}</span></p>
                    <p><strong>Estimated ROI:</strong> {analysis['roi_percentage']}%</p>
                    <p><strong>Estimated Monthly Rent:</strong> ${analysis['estimated_rent']:,}</p>
                    <p><strong>Estimated Monthly Cash Flow:</strong> ${analysis['cash_flow']:,}</p>
                </div>

                <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <p style="margin: 0;"><strong>⚡ Action Required:</strong> This property was just listed and meets your Section 8 investment criteria. Consider reviewing immediately.</p>
                </div>

                <p style="color: #6c757d; font-size: 12px; margin-top: 30px;">
                    This alert was generated by Section 8 Investor Pro at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                </p>
            </body>
            </html>
            """

            # Send email using Resend
            params = {
                "from": "Section 8 Monitor <<EMAIL>>",
                "to": [self.alert_email],
                "subject": subject,
                "html": html_content
            }

            email_result = resend.Emails.send(params)

            # Log email attempt
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO email_alerts (property_id, alert_type, email_status, error_message)
                    VALUES ($1, $2, $3, $4)
                """, property_id, 'high_priority_alert', 'sent' if email_result else 'failed',
                str(email_result) if not email_result else None)

            if email_result:
                self.daily_stats['alerts_sent'] += 1
                self.logger.info(f"Alert sent for property: {property_data.get('address', '')}")
                return True
            else:
                self.logger.error(f"Failed to send alert for property: {property_data.get('address', '')}")
                return False

        except Exception as e:
            self.logger.error(f"Error sending property alert: {e}")
            return False

    async def process_city(self, city_data: Dict[str, str]) -> int:
        """Process a single city for new properties"""
        city = city_data['city']
        state = city_data['state']
        properties_found = 0

        try:
            self.logger.info(f"Processing {city}, {state}")

            # Get FMR data for the city
            fmr_data = await self.get_hud_fmr_data(city, state)

            # Search for properties
            properties = await self.search_properties_in_city(city, state)

            for property_data in properties:
                try:
                    # Calculate investment analysis
                    analysis = self.calculate_investment_score(property_data, fmr_data)

                    # Only process properties with decent scores
                    if analysis['investment_priority'] in ['HIGH', 'MEDIUM']:
                        # Save to database
                        property_id = await self.save_property_to_database(property_data, analysis, fmr_data)

                        if property_id:
                            properties_found += 1
                            self.daily_stats['properties_analyzed'] += 1

                            # Send alert for high priority properties
                            if analysis['investment_priority'] == 'HIGH':
                                await self.send_property_alert(property_data, analysis, property_id)
                                self.daily_stats['high_priority_found'] += 1

                            self.logger.info(f"Found {analysis['investment_priority']} priority property: {property_data.get('address', '')} - Score: {analysis['investment_score']}")

                    # Rate limiting
                    await asyncio.sleep(1)

                except Exception as e:
                    self.logger.error(f"Error processing property in {city}, {state}: {e}")
                    continue

            self.daily_stats['markets_processed'] += 1
            self.logger.info(f"Completed {city}, {state}: {properties_found} properties found")

            return properties_found

        except Exception as e:
            self.logger.error(f"Error processing city {city}, {state}: {e}")
            self.daily_stats['errors_count'] += 1
            return 0

    async def run_monitoring_cycle(self):
        """Run a complete monitoring cycle"""
        cycle_start = datetime.now()
        total_properties = 0

        try:
            self.logger.info("Starting monitoring cycle")

            # Process cities with limited concurrency
            semaphore = asyncio.Semaphore(3)  # Limit concurrent city processing

            async def process_with_semaphore(city_data):
                async with semaphore:
                    return await self.process_city(city_data)

            # Process all target cities
            tasks = [process_with_semaphore(city_data) for city_data in self.target_cities]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Count successful results
            for result in results:
                if isinstance(result, int):
                    total_properties += result
                elif isinstance(result, Exception):
                    self.logger.error(f"City processing failed: {result}")

            # Update daily statistics
            await self.update_daily_stats()

            cycle_duration = (datetime.now() - cycle_start).total_seconds()
            self.logger.info(f"Monitoring cycle completed: {total_properties} properties found in {cycle_duration:.1f}s")

        except Exception as e:
            self.logger.error(f"Error in monitoring cycle: {e}")
            self.daily_stats['errors_count'] += 1

    async def update_daily_stats(self):
        """Update daily statistics in database"""
        try:
            today = datetime.now().date()

            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO monitoring_stats (
                        date, properties_analyzed, high_priority_found, alerts_sent,
                        api_calls_made, markets_processed, errors_count
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                    ON CONFLICT (date) DO UPDATE SET
                        properties_analyzed = monitoring_stats.properties_analyzed + EXCLUDED.properties_analyzed,
                        high_priority_found = monitoring_stats.high_priority_found + EXCLUDED.high_priority_found,
                        alerts_sent = monitoring_stats.alerts_sent + EXCLUDED.alerts_sent,
                        api_calls_made = monitoring_stats.api_calls_made + EXCLUDED.api_calls_made,
                        markets_processed = monitoring_stats.markets_processed + EXCLUDED.markets_processed,
                        errors_count = monitoring_stats.errors_count + EXCLUDED.errors_count
                """, today, self.daily_stats['properties_analyzed'], self.daily_stats['high_priority_found'],
                self.daily_stats['alerts_sent'], self.daily_stats['api_calls_made'],
                self.daily_stats['markets_processed'], self.daily_stats['errors_count'])

        except Exception as e:
            self.logger.error(f"Error updating daily stats: {e}")

    async def send_daily_summary(self):
        """Send daily summary email"""
        try:
            today = datetime.now().date()

            # Get today's statistics
            async with self.db_pool.acquire() as conn:
                stats = await conn.fetchrow("""
                    SELECT * FROM monitoring_stats WHERE date = $1
                """, today)

                # Get top properties found today
                top_properties = await conn.fetch("""
                    SELECT address, city, state, investment_score, investment_priority, roi_percentage, cash_flow
                    FROM property_leads
                    WHERE DATE(discovered_at) = $1
                    ORDER BY investment_score DESC
                    LIMIT 10
                """, today)

            if not stats:
                return

            # Create summary email
            subject = f"📊 Daily Section 8 Monitor Summary - {today.strftime('%B %d, %Y')}"

            properties_html = ""
            for prop in top_properties:
                properties_html += f"""
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">{prop['address']}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">{prop['city']}, {prop['state']}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">{prop['investment_score']}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">{prop['investment_priority']}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">{prop['roi_percentage']}%</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${prop['cash_flow']}</td>
                </tr>
                """

            html_content = f"""
            <html>
            <body style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;">
                <h2 style="color: #2c5aa0;">📊 Daily Section 8 Monitor Summary</h2>
                <p><strong>Date:</strong> {today.strftime('%B %d, %Y')}</p>

                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #28a745; margin-top: 0;">Daily Statistics</h3>
                    <p><strong>Properties Analyzed:</strong> {stats['properties_analyzed']}</p>
                    <p><strong>High Priority Found:</strong> {stats['high_priority_found']}</p>
                    <p><strong>Alerts Sent:</strong> {stats['alerts_sent']}</p>
                    <p><strong>Markets Processed:</strong> {stats['markets_processed']}</p>
                    <p><strong>API Calls Made:</strong> {stats['api_calls_made']}</p>
                    <p><strong>Errors:</strong> {stats['errors_count']}</p>
                </div>

                <div style="margin: 20px 0;">
                    <h3 style="color: #2c5aa0;">Top Properties Found Today</h3>
                    <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                        <thead>
                            <tr style="background-color: #e9ecef;">
                                <th style="padding: 10px; text-align: left; border-bottom: 2px solid #ddd;">Address</th>
                                <th style="padding: 10px; text-align: left; border-bottom: 2px solid #ddd;">Location</th>
                                <th style="padding: 10px; text-align: left; border-bottom: 2px solid #ddd;">Score</th>
                                <th style="padding: 10px; text-align: left; border-bottom: 2px solid #ddd;">Priority</th>
                                <th style="padding: 10px; text-align: left; border-bottom: 2px solid #ddd;">ROI</th>
                                <th style="padding: 10px; text-align: left; border-bottom: 2px solid #ddd;">Cash Flow</th>
                            </tr>
                        </thead>
                        <tbody>
                            {properties_html}
                        </tbody>
                    </table>
                </div>

                <p style="color: #6c757d; font-size: 12px; margin-top: 30px;">
                    Generated by Section 8 Investor Pro at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                </p>
            </body>
            </html>
            """

            # Send summary email
            params = {
                "from": "Section 8 Monitor <<EMAIL>>",
                "to": [self.alert_email],
                "subject": subject,
                "html": html_content
            }

            result = resend.Emails.send(params)

            if result:
                self.logger.info("Daily summary email sent successfully")
            else:
                self.logger.error("Failed to send daily summary email")

        except Exception as e:
            self.logger.error(f"Error sending daily summary: {e}")

    async def start_monitoring(self):
        """Start the main monitoring loop"""
        self.running = True
        last_summary_date = None

        try:
            self.logger.info("Starting Section 8 Production Monitor")
            self.logger.info(f"Monitoring {len(self.target_cities)} cities every 1 minute")
            self.logger.info(f"Email alerts: {self.alert_email}")

            while self.running:
                try:
                    # Run monitoring cycle
                    await self.run_monitoring_cycle()

                    # Send daily summary if it's a new day
                    current_date = datetime.now().date()
                    if last_summary_date != current_date and datetime.now().hour >= 18:  # Send summary after 6 PM
                        await self.send_daily_summary()
                        last_summary_date = current_date

                    # Wait for next cycle (1 minute)
                    if self.running:
                        self.logger.info("Waiting 60 seconds for next cycle...")
                        await asyncio.sleep(60)

                except KeyboardInterrupt:
                    self.logger.info("Shutdown requested by user")
                    break
                except Exception as e:
                    self.logger.error(f"Error in main monitoring loop: {e}")
                    self.daily_stats['errors_count'] += 1
                    # Wait before retrying
                    await asyncio.sleep(30)

        finally:
            self.running = False
            await self.cleanup()

    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.session:
                await self.session.close()

            if self.db_pool:
                await self.db_pool.close()

            self.logger.info("Cleanup completed")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

# Main execution
async def main():
    """Main function"""
    monitor = Section8ProductionMonitor()

    try:
        await monitor.initialize()
        await monitor.start_monitoring()
    except KeyboardInterrupt:
        print("\nShutdown requested...")
    except Exception as e:
        print(f"Fatal error: {e}")
        traceback.print_exc()
    finally:
        await monitor.cleanup()

if __name__ == "__main__":
    # Run the monitor
    asyncio.run(main())
