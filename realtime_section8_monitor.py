#!/usr/bin/env python3
"""
Real-time Section 8 Property Monitor
Production-ready monitoring system with 1-minute intervals and real API integration
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.config import Config
from src.services.realtime_monitor import RealtimePropertyMonitor
from src.utils.logger import setup_logging

class Section8MonitorApp:
    """Main application class for real-time Section 8 monitoring"""
    
    def __init__(self):
        self.config = Config()
        self.logger = setup_logging(self.config.LOG_LEVEL)
        self.monitor = None
        self.running = False
        
    async def start(self):
        """Start the monitoring application"""
        self.logger.info("=" * 60)
        self.logger.info("Section 8 Property Monitor - Real-time Version")
        self.logger.info("=" * 60)
        self.logger.info(f"Started at: {datetime.now().isoformat()}")
        self.logger.info(f"Scan interval: 1 minute")
        self.logger.info(f"API endpoint: {self.config.REAL_ESTATE_API_URL}")
        self.logger.info(f"Database: {self.config.DATABASE_URL.split('@')[1] if '@' in self.config.DATABASE_URL else 'Local'}")
        
        try:
            # Initialize monitor
            self.monitor = RealtimePropertyMonitor(self.config)
            await self.monitor.initialize()
            
            # Set up signal handlers for graceful shutdown
            self.setup_signal_handlers()
            
            # Start monitoring
            self.running = True
            self.logger.info("Starting real-time property monitoring...")
            await self.monitor.start_monitoring()
            
        except KeyboardInterrupt:
            self.logger.info("Received interrupt signal")
        except Exception as e:
            self.logger.error(f"Application error: {str(e)}")
            raise
        finally:
            await self.cleanup()
    
    def setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            self.running = False
            if self.monitor:
                asyncio.create_task(self.monitor.stop_monitoring())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def cleanup(self):
        """Clean up resources"""
        self.logger.info("Cleaning up resources...")
        if self.monitor:
            await self.monitor.close()
        self.logger.info("Application shutdown complete")
    
    async def status_check(self):
        """Perform a status check and return system information"""
        if not self.monitor:
            return {"status": "not_initialized"}
        
        return {
            "status": "running" if self.running else "stopped",
            "monitor_status": self.monitor.get_status(),
            "config": {
                "scan_interval": "1 minute",
                "markets_monitored": len(self.config.get_all_markets()),
                "api_endpoint": self.config.REAL_ESTATE_API_URL
            }
        }

async def main():
    """Main entry point"""
    app = Section8MonitorApp()
    
    # Check if this is a status check
    if len(sys.argv) > 1 and sys.argv[1] == "status":
        status = await app.status_check()
        print(f"Status: {status}")
        return
    
    # Start the application
    await app.start()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
    except Exception as e:
        print(f"Application failed: {str(e)}")
        sys.exit(1)
